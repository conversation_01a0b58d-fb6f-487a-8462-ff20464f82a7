{"name": "Sim Dev Environment", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/workspace", "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached", "customizations": {"vscode": {"settings": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.profiles.linux": {"bash": {"path": "/bin/bash", "args": ["--login"]}}, "terminal.integrated.shellIntegration.enabled": true}, "extensions": ["biomejs.biome", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "github.copilot", "github.copilot-chat", "mikestead.dotenv", "dsznajder.es7-react-js-snippets", "steoates.autoimport", "oven.bun-vscode"]}}, "forwardPorts": [3000, 5432], "postCreateCommand": "bash -c 'bash .devcontainer/post-create.sh || true'", "postStartCommand": "bash -c 'if [ ! -f ~/.bashrc ] || ! grep -q \"sim-start\" ~/.bashrc; then cp .devcontainer/.bashrc ~/.bashrc; fi'", "remoteUser": "bun", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/prulloac/devcontainer-features/bun:1": {"version": "latest"}}}