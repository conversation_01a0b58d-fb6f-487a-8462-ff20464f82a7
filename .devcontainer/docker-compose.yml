services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/workspace:cached
      - bun-cache:/home/<USER>/.bun/cache:delegated
    command: sleep infinity
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/simstudio
      - POSTGRES_URL=**************************************/simstudio
      - BETTER_AUTH_URL=http://localhost:3000
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - BUN_INSTALL_CACHE_DIR=/home/<USER>/.bun/cache
    depends_on:
      db:
        condition: service_healthy
      realtime:
        condition: service_healthy
      migrations:
        condition: service_completed_successfully
    ports:
      - "3000:3000"
      - "3001:3001"
    working_dir: /workspace
    healthcheck:
      test: ['CMD', 'wget', '--spider', '--quiet', 'http://127.0.0.1:3000']
      interval: 90s
      timeout: 5s
      retries: 3
      start_period: 10s

  realtime:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    command: sleep infinity
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/simstudio
      - BETTER_AUTH_URL=http://localhost:3000
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "3002:3002"
    working_dir: /workspace
    healthcheck:
      test: ['CMD', 'wget', '--spider', '--quiet', 'http://127.0.0.1:3002']
      interval: 90s
      timeout: 5s
      retries: 3
      start_period: 10s

  migrations:
    build:
      context: ..
      dockerfile: docker/db.Dockerfile
    environment:
      - DATABASE_URL=**************************************/simstudio
    depends_on:
      db:
        condition: service_healthy
    command: ['bun', 'run', 'db:migrate']
    restart: 'no'

  db:
    image: pgvector/pgvector:pg17
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=simstudio
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres-data:
  bun-cache:
