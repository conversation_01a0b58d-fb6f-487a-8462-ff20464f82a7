import type { SVGProps } from 'react'

export function ApiIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M5.61111 24.3889C8.5 27.2778 12.4722 25.4722 13.5556 24.3889L15.7222 22.2222L7.77778 14.2778L5.61111 16.4444C4.52778 17.5278 2.72222 21.5 5.61111 24.3889ZM5.61111 24.3889L2 28M24.3889 5.61111C21.5 2.72222 17.5278 4.52778 16.4444 5.61111L14.2778 7.77778L22.2222 15.7222L24.3889 13.5556C25.4722 12.4722 27.2778 8.5 24.3889 5.61111ZM24.3889 5.61111L28 2M15.7222 9.22222L12.8333 12.1111M20.7778 14.2778L17.8889 17.1667'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ConditionalIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='28'
      height='29'
      viewBox='0 0 28 29'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M23.1015 1.01616C22.1825 1.01442 21.2926 1.33795 20.5894 1.92946C19.8861 2.52098 19.4149 3.34229 19.2592 4.24794C19.1035 5.15358 19.2733 6.08512 19.7386 6.87755C20.2039 7.66998 20.9346 8.27217 21.8014 8.57745V12.7169H6.20035V8.57745C7.06779 8.27077 7.79888 7.6673 8.26441 6.87372C8.72994 6.08013 8.89994 5.14752 8.74435 4.24072C8.58877 3.33391 8.11762 2.5113 7.41419 1.91828C6.71075 1.32526 5.82032 1 4.90027 1C3.98021 1 3.08978 1.32526 2.38634 1.91828C1.68291 2.5113 1.21176 3.33391 1.05618 4.24072C0.900594 5.14752 1.07059 6.08013 1.53612 6.87372C2.00165 7.6673 2.73274 8.27077 3.60018 8.57745V12.7169C3.60018 13.4065 3.87413 14.0679 4.36175 14.5555C4.84938 15.0432 5.51074 15.3171 6.20035 15.3171H12.7008V20.7567C11.8333 21.0633 11.1023 21.6668 10.6367 22.4604C10.1712 23.254 10.0012 24.1866 10.1568 25.0934C10.3124 26.0002 10.7835 26.8228 11.4869 27.4158C12.1904 28.0089 13.0808 28.3341 14.0009 28.3341C14.9209 28.3341 15.8114 28.0089 16.5148 27.4158C17.2182 26.8228 17.6894 26.0002 17.845 25.0934C18.0005 24.1866 17.8305 23.254 17.365 22.4604C16.8995 21.6668 16.1684 21.0633 15.301 20.7567V15.3171H21.8014C22.491 15.3171 23.1524 15.0432 23.64 14.5555C24.1276 14.0679 24.4015 13.4065 24.4015 12.7169V8.57745C25.2683 8.27217 25.999 7.66998 26.4643 6.87755C26.9296 6.08512 27.0994 5.15358 26.9437 4.24794C26.788 3.34229 26.3168 2.52098 25.6135 1.92946C24.9103 1.33795 24.0204 1.01442 23.1015 1.01616ZM4.90027 6.2165C4.64313 6.2165 4.39177 6.14025 4.17798 5.99739C3.96418 5.85454 3.79754 5.65149 3.69914 5.41393C3.60074 5.17637 3.575 4.91497 3.62516 4.66278C3.67532 4.41059 3.79915 4.17893 3.98097 3.99711C4.16279 3.81529 4.39444 3.69147 4.64663 3.64131C4.89882 3.59114 5.16023 3.61689 5.39779 3.71529C5.63535 3.81369 5.83839 3.98033 5.98125 4.19412C6.1241 4.40792 6.20035 4.65928 6.20035 4.91641C6.20035 5.26122 6.06338 5.5919 5.81956 5.83571C5.57575 6.07953 5.24507 6.2165 4.90027 6.2165ZM14.0009 25.7178C13.7437 25.7178 13.4924 25.6415 13.2786 25.4987C13.0648 25.3558 12.8981 25.1528 12.7997 24.9152C12.7013 24.6777 12.6756 24.4163 12.7258 24.1641C12.7759 23.9119 12.8997 23.6802 13.0816 23.4984C13.2634 23.3166 13.495 23.1928 13.7472 23.1426C13.9994 23.0924 14.2608 23.1182 14.4984 23.2166C14.7359 23.315 14.939 23.4816 15.0818 23.6954C15.2247 23.9092 15.301 24.1606 15.301 24.4177C15.301 24.7625 15.164 25.0932 14.9202 25.337C14.6764 25.5808 14.3457 25.7178 14.0009 25.7178ZM23.1015 6.2165C22.8443 6.2165 22.593 6.14025 22.3792 5.99739C22.1654 5.85454 21.9987 5.65149 21.9003 5.41393C21.8019 5.17637 21.7762 4.91497 21.8264 4.66278C21.8765 4.41059 22.0003 4.17893 22.1822 3.99711C22.364 3.81529 22.5956 3.69147 22.8478 3.64131C23.1 3.59114 23.3614 3.61689 23.599 3.71529C23.8365 3.81369 24.0396 3.98033 24.1824 4.19412C24.3253 4.40792 24.4015 4.65928 24.4015 4.91641C24.4015 5.26122 24.2646 5.5919 24.0208 5.83571C23.777 6.07953 23.4463 6.2165 23.1015 6.2165Z'
        fill='currentColor'
        stroke='currentColor'
        strokeWidth='0.25'
      />
    </svg>
  )
}

export function AgentIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='21'
      height='24'
      viewBox='0 0 21 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15.6667 9.25H4.66667C2.64162 9.25 1 10.8916 1 12.9167V18.4167C1 20.4417 2.64162 22.0833 4.66667 22.0833H15.6667C17.6917 22.0833 19.3333 20.4417 19.3333 18.4167V12.9167C19.3333 10.8916 17.6917 9.25 15.6667 9.25Z'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.1663 5.58464C11.1789 5.58464 11.9997 4.76382 11.9997 3.7513C11.9997 2.73878 11.1789 1.91797 10.1663 1.91797C9.15382 1.91797 8.33301 2.73878 8.33301 3.7513C8.33301 4.76382 9.15382 5.58464 10.1663 5.58464Z'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.167 5.58594V9.2526M7.41699 16.5859V14.7526M12.917 14.7526V16.5859'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function CodeIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='27'
      viewBox='0 0 30 27'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M23.2639 6.83064L23.6375 7.20422C26.5433 10.1117 27.9971 11.5638 27.9971 13.37C27.9971 15.1762 26.5433 16.63 23.6375 19.5358L23.2639 19.9094M18.0434 2L11.9507 24.7401M6.72863 6.83064L6.35504 7.20422C3.45081 10.1117 1.99707 11.5638 1.99707 13.37C1.99707 15.1762 3.45081 16.63 6.35829 19.5358L6.73187 19.9094'
        stroke='currentColor'
        strokeWidth='2.6'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export function ChartBarIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='30'
      height='30'
      viewBox='0 0 30 30'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M19.3333 11.2863V4.88889C19.3333 4.12271 19.029 3.38791 18.4872 2.84614C17.9454 2.30436 17.2106 2 16.4444 2H13.5556C12.7894 2 12.0546 2.30436 11.5128 2.84614C10.971 3.38791 10.6667 4.12271 10.6667 4.88889V16.8576M19.3333 11.2863V28M19.3333 11.2863H25.1111C25.8773 11.2863 26.6121 11.5907 27.1539 12.1325C27.6956 12.6742 28 13.409 28 14.1752V25.1111C28 25.8773 27.6956 26.6121 27.1539 27.1539C26.6121 27.6956 25.8773 28 25.1111 28H19.3333M10.6667 16.8576V28M10.6667 16.8576H4.88889C4.12271 16.8576 3.38791 17.1619 2.84614 17.7037C2.30436 18.2455 2 18.9803 2 19.7464V25.1111C2 25.8773 2.30436 26.6121 2.84614 27.1539C3.38791 27.6956 4.12271 28 4.88889 28H10.6667M19.3333 28H10.6667'
        stroke='currentColor'
        strokeWidth='2.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export const ConnectIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    {...props}
    width='24'
    height='24'
    viewBox='-2 -2 28 28'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M24 16C24 17.4667 23.4778 18.7222 22.4333 19.7667C21.3889 20.8111 20.1333 21.3333 18.6667 21.3333H7.76667C7.47778 22.1111 6.99467 22.7498 6.31733 23.2493C5.64 23.7489 4.86756 23.9991 4 24C2.88889 24 1.94444 23.6111 1.16667 22.8333C0.388889 22.0556 0 21.1111 0 20C0 18.8889 0.388889 17.9444 1.16667 17.1667C1.94444 16.3889 2.88889 16 4 16C4.86667 16 5.63911 16.2498 6.31733 16.7493C6.99556 17.2489 7.47867 17.888 7.76667 18.6667H18.6667C19.4 18.6667 20.028 18.4053 20.5507 17.8827C21.0733 17.36 21.3342 16.7324 21.3333 16C21.3324 15.2676 21.0716 14.6396 20.5507 14.116C20.0298 13.5924 19.4018 13.3316 18.6667 13.3333L5.33333 13.3333C3.86667 13.3333 2.61111 12.8111 1.56667 11.7667C0.522222 10.7222 0 9.46667 0 8C0 6.53334 0.522222 5.27778 1.56667 4.23334C2.61111 3.18889 3.86667 2.66667 5.33333 2.66667H16.2333C16.5222 1.88889 17.0058 1.24978 17.684 0.749337C18.3622 0.248893 19.1342 -0.000885312 20 3.57628e-06C21.1111 3.57628e-06 22.0556 0.388892 22.8333 1.16667C23.6111 1.94445 24 2.88889 24 4C24 5.11111 23.6111 6.05556 22.8333 6.83334C22.0556 7.61111 21.1111 8 20 8C19.1333 8 18.3556 7.74978 17.6667 7.24934C16.9778 6.74889 16.5 6.11022 16.2333 5.33334H5.33333C4.6 5.33334 3.97244 5.59422 3.45067 6.116C2.92889 6.63778 2.66756 7.26578 2.66667 8C2.66578 8.73422 2.92711 9.36178 3.45067 9.88267C3.97422 10.4036 4.60178 10.6649 5.33333 10.6667L18.6667 10.6667C20.1333 10.6667 21.3889 11.1889 22.4333 12.2333C23.4778 13.2778 24 14.5333 24 16ZM5.33333 20C5.33333 19.6222 5.20533 19.3053 4.94933 19.0493C4.69333 18.7933 4.37689 18.6658 4 18.6667C3.62311 18.6676 3.30667 18.7956 3.05067 19.0507C2.79467 19.3058 2.66667 19.6222 2.66667 20C2.66667 20.3778 2.79467 20.6942 3.05067 20.9493C3.30667 21.2044 3.62311 21.3324 4 21.3333C4.37689 21.3342 4.69378 21.2062 4.95067 20.9493C5.20756 20.6924 5.33511 20.376 5.33333 20ZM21.3333 4C21.3333 3.62223 21.2053 3.30534 20.9493 3.04934C20.6933 2.79334 20.3769 2.66578 20 2.66667C19.6231 2.66756 19.3067 2.79556 19.0507 3.05067C18.7947 3.30578 18.6667 3.62223 18.6667 4C18.6667 4.37778 18.7947 4.69422 19.0507 4.94934C19.3067 5.20445 19.6231 5.33245 20 5.33334C20.3769 5.33423 20.6938 5.20622 20.9507 4.94934C21.2076 4.69245 21.3351 4.376 21.3333 4Z'
      fill='currentColor'
    />
  </svg>
)

export const GmailIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      width='96px'
      height='96px'
      {...props}
    >
      <path fill='#4caf50' d='M45,16.2l-5,2.75l-5,4.75L35,40h7c1.657,0,3-1.343,3-3V16.2z' />
      <path fill='#1e88e5' d='M3,16.2l3.614,1.71L13,23.7V40H6c-1.657,0-3-1.343-3-3V16.2z' />
      <polygon
        fill='#e53935'
        points='35,11.2 24,19.45 13,11.2 12,17 13,23.7 24,31.95 35,23.7 36,17'
      />
      <path
        fill='#c62828'
        d='M3,12.298V16.2l10,7.5V11.2L9.876,8.859C9.132,8.301,8.228,8,7.298,8h0C4.924,8,3,9.924,3,12.298z'
      />
      <path
        fill='#fbc02d'
        d='M45,12.298V16.2l-10,7.5V11.2l3.124-2.341C38.868,8.301,39.772,8,40.702,8h0 C43.076,8,45,9.924,45,12.298z'
      />
    </svg>
  )
}

export function FirecrawlIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 642 600' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M301 63C299 91 303 122 298 149C295 158 289 165 283 169C274 172 266 170 261 167C253 176 248 183 244 191C230 226 226 263 226 301C216 310 203 317 192 310C179 295 175 277 174 259C161 273 153 288 146 304C141 321 138 336 137 352C140 372 145 388 152 402C161 421 174 435 187 449C181 462 165 453 157 450C158 454 161 458 165 461C195 490 231 500 268 509C240 494 211 471 195 442C179 413 172 378 180 344C191 353 200 362 211 364C223 365 232 361 236 353C247 274 299 214 323 143C322 136 327 140 329 142C354 165 367 191 375 218C387 254 381 294 379 329C393 345 413 334 424 329C429 342 432 352 429 362C427 378 417 388 413 400C422 407 433 403 440 400C432 423 419 442 404 460C383 483 358 501 335 512C379 502 420 491 449 459C443 458 427 464 428 452C443 437 464 423 472 403C482 383 485 362 484 339C482 307 472 280 458 254C459 267 452 276 445 284C434 289 426 279 424 272C415 247 424 220 418 198C415 179 405 165 397 150C370 114 336 86 303 64'
        fill='rgb(253,76,31)'
      />
      <path
        d='M324 141C303 214 249 273 244 354C235 359 229 364 223 366C205 367 193 357 182 347C180 350 179 353 180 357C178 374 178 390 182 403C185 421 193 434 200 448C212 465 227 480 243 491C258 500 269 513 285 512C284 508 257 485 252 468C241 450 235 433 233 414C241 415 254 420 263 412C260 387 265 363 273 343C281 323 293 306 310 295C317 289 324 285 330 282C328 307 328 331 329 355C330 368 332 379 338 389C358 394 376 384 388 370C383 386 377 401 371 415C376 414 381 411 385 408C383 421 380 431 376 441C366 467 356 491 334 510C358 499 381 483 400 461C418 442 430 423 440 403C432 404 421 410 413 404C414 386 428 377 427 360C429 349 428 340 424 332C413 336 404 341 392 339C386 338 381 334 379 330C380 292 385 248 371 214C366 195 358 180 349 165C341 155 333 145 323 140'
        fill='rgb(254,156,69)'
      />
      <path
        d='M330 284C309 293 289 311 279 332C267 356 261 383 265 411C256 420 242 418 235 412C237 438 245 459 258 479C269 493 281 507 295 513C288 495 265 472 265 446C272 447 281 454 288 444C296 425 303 407 309 388C317 406 321 427 336 443C346 449 358 446 363 438C355 464 348 489 334 511C344 501 352 491 357 480C370 457 379 435 385 412C380 411 376 416 371 418C376 401 382 386 387 371C379 382 369 388 358 391C348 394 337 392 334 383C324 353 328 316 330 285'
        fill='rgb(254,220,87)'
      />
      <path
        d='M311 389C303 407 297 426 289 445C282 454 273 450 268 445C267 472 285 492 302 512C299 514 297 514 294 514C297 514 299 514 301 514C314 515 325 512 334 513C341 495 355 467 362 443C357 446 351 448 344 447C337 446 334 441 330 438C320 422 316 406 311 391'
        fill='rgb(251,250,202)'
      />
      <path
        d='M187 163C188 181 167 187 164 203C158 215 158 228 159 241C172 233 183 221 188 209C193 194 192 178 188 166'
        fill='rgb(253,76,31)'
      />
    </svg>
  )
}

export function ExaAIIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      width='252'
      height='304'
      viewBox='0 0 252 304'
      fill='none'
      role='img'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.82461 0.75046C5.94359 0.750786 5.94359 0.750786 7.08518 0.751118C7.94546 0.74652 8.80574 0.741921 9.69209 0.737183C10.6428 0.742326 11.5934 0.74747 12.5729 0.752769C13.5785 0.750355 14.584 0.74794 15.62 0.745453C19.0112 0.739788 22.4021 0.7485 25.7932 0.757087C28.2155 0.756015 30.6378 0.754077 33.0601 0.751335C38.9691 0.746979 44.878 0.751943 50.787 0.761185C57.6619 0.771553 64.5368 0.771499 71.4118 0.771013C83.674 0.770512 95.9362 0.779683 108.198 0.794197C120.11 0.808288 132.021 0.815367 143.932 0.814705C144.666 0.814665 145.399 0.814625 146.155 0.814584C146.888 0.814543 147.62 0.814503 148.375 0.814461C161.918 0.813772 175.461 0.819657 189.005 0.828614C193.802 0.831636 198.6 0.832695 203.397 0.833147C209.848 0.833898 216.298 0.840223 222.749 0.850313C225.124 0.853246 227.498 0.854462 229.873 0.853885C233.1 0.853398 236.327 0.859013 239.554 0.866295C240.507 0.864746 241.46 0.863197 242.442 0.861602C248.886 0.885829 248.886 0.885829 250 2.00001C252.1 17.5148 252.1 17.5148 247.828 23.7344C245.548 26.539 243.019 29.0818 240.419 31.5889C238.376 33.6204 236.637 35.8493 234.875 38.125C231.086 42.8997 227.239 47.6241 223.369 52.3335C222.383 53.5339 221.397 54.7348 220.412 55.936C214.429 63.2301 208.439 70.5132 202.313 77.6875C198.113 82.6097 194.1 87.6563 190.129 92.7637C186.095 97.9301 181.879 102.925 177.625 107.91C173.674 112.544 169.843 117.276 166 122C164.501 123.834 163.001 125.667 161.5 127.5C159.202 130.308 156.905 133.116 154.609 135.926C153.831 136.878 153.052 137.831 152.25 138.812C151.515 139.719 150.78 140.625 150.023 141.559C148.814 143.018 147.577 144.457 146.289 145.848C143.623 148.63 143.623 148.63 143.078 152.293C144.286 155.841 146.405 158.206 148.875 160.938C149.881 162.085 150.885 163.235 151.887 164.387C152.661 165.275 152.661 165.275 153.451 166.181C155.961 169.128 158.321 172.187 160.688 175.25C164.273 179.87 167.986 184.285 171.922 188.613C174.481 191.552 176.863 194.608 179.25 197.688C183.104 202.657 187.072 207.495 191.188 212.25C195.142 216.819 198.998 221.438 202.746 226.18C206.376 230.722 210.089 235.197 213.79 239.682C217.101 243.695 220.403 247.715 223.688 251.75C226.934 255.727 230.237 259.651 233.563 263.562C238.148 268.958 242.642 274.418 247 280C247.845 280.841 247.845 280.841 248.707 281.699C250.778 285.385 250.314 289.106 250.188 293.25C250.174 294.09 250.16 294.931 250.146 295.797C250.111 297.865 250.057 299.933 250 302C247.719 303.141 246.468 303.126 243.933 303.129C243.077 303.132 242.221 303.135 241.339 303.138C240.393 303.137 239.447 303.135 238.472 303.134C237.471 303.136 236.47 303.138 235.439 303.141C232.064 303.147 228.689 303.146 225.314 303.145C222.904 303.148 220.493 303.152 218.082 303.155C212.201 303.163 206.32 303.166 200.438 303.167C195.66 303.168 190.882 303.17 186.104 303.173C172.568 303.182 159.032 303.186 145.496 303.185C144.766 303.185 144.036 303.185 143.284 303.185C142.553 303.185 141.822 303.185 141.068 303.185C129.214 303.185 117.359 303.194 105.504 303.208C93.3424 303.223 81.1812 303.23 69.0199 303.229C62.1877 303.229 55.3555 303.231 48.5233 303.242C42.1015 303.252 35.6798 303.252 29.258 303.245C26.8948 303.243 24.5316 303.246 22.1685 303.252C18.955 303.26 15.7417 303.255 12.5282 303.247C11.5821 303.252 10.636 303.258 9.66125 303.263C8.80507 303.258 7.94889 303.254 7.06677 303.249C6.32436 303.249 5.58195 303.249 4.81705 303.25C3 303 3 303 1 301C0.749304 298.843 0.749304 298.843 0.74832 296.106C0.743105 295.065 0.737889 294.024 0.732515 292.952C0.736977 291.799 0.741439 290.647 0.746035 289.46C0.742915 288.243 0.739795 287.025 0.73658 285.771C0.729961 282.382 0.732374 278.993 0.738553 275.603C0.743062 271.952 0.736102 268.301 0.730593 264.65C0.721681 257.494 0.723629 250.337 0.729418 243.18C0.733928 237.366 0.734551 231.552 0.732371 225.738C0.732064 224.912 0.731758 224.085 0.731442 223.234C0.730803 221.556 0.729497 219.878 0.729497 218.2C0.723788 202.448 0.73036 186.696 0.741098 170.944C0.750034 157.416 0.748482 143.888 0.73926 130.36C0.728567 114.666 0.724337 98.9726 0.730485 83.2787C0.731122 81.6065 0.731751 79.9343 0.732371 78.2621C0.732834 77.0279 0.732834 77.0279 0.733306 75.7687C0.735036 69.9587 0.732112 64.1487 0.727412 58.3387C0.721814 51.2623 0.723297 44.1858 0.733973 37.1094C0.739244 33.496 0.741262 29.8827 0.734675 26.2694C0.727655 22.3596 0.736014 18.45 0.746035 14.5403C0.741574 13.388 0.737112 12.2357 0.732515 11.0484C0.737731 10.0076 0.742947 8.96683 0.74832 7.89449C0.748645 6.99108 0.748969 6.08767 0.749304 5.15689C1.07777 2.33093 1.9319 1.14609 4.82461 0.75046ZM39 25C40.5046 28.0092 41.6553 29.9923 43.7227 32.5039C44.2563 33.1562 44.79 33.8084 45.3398 34.4805C45.9083 35.1675 46.4768 35.8546 47.0625 36.5625C47.6503 37.2792 48.2381 37.9959 48.8438 38.7344C50.5594 40.8255 52.2795 42.9128 54 45C55.5008 46.8327 57.0009 48.6659 58.5 50.5C59.2038 51.3598 59.9077 52.2196 60.6328 53.1055C74.8482 70.4877 74.8482 70.4877 81.4375 78.9375C86.6547 85.6225 92.0434 92.1594 97.4453 98.6953C106.496 109.647 115.413 120.68 124 132C126.944 131.669 127.951 131.051 130.043 128.883C130.751 127.973 131.458 127.063 132.188 126.125C132.978 125.125 133.768 124.124 134.582 123.094C135.38 122.073 136.178 121.052 137 120C138.453 118.204 139.911 116.412 141.375 114.625C142.114 113.721 142.852 112.818 143.613 111.887C146.237 108.714 148.896 105.574 151.563 102.438C155.359 97.9705 159.069 93.4497 162.711 88.8555C167.531 82.8433 172.464 76.9234 177.368 70.98C181.651 65.7834 185.911 60.5683 190.16 55.3438C193.828 50.8413 197.525 46.3703 201.296 41.9539C209.025 34.2599 209.025 34.2599 213 25C155.58 25 98.16 25 39 25ZM25 48C25 78.36 25 108.72 25 140C49.42 140 73.84 140 99 140C97.1724 135.431 95.234 132.634 92.0625 129.062C88.1461 124.547 84.3496 119.974 80.6875 115.25C75.8962 109.087 70.9584 103.054 65.9834 97.0383C62.3144 92.6007 58.6856 88.1356 55.1016 83.6289C51.8102 79.5116 48.4818 75.4244 45.1528 71.3374C42.2047 67.7174 39.266 64.0903 36.3438 60.4492C35.7523 59.7146 35.1609 58.98 34.5515 58.2231C33.4108 56.8059 32.2728 55.3863 31.1379 53.9644C28.4954 50.4896 28.4954 50.4896 25 48ZM25 164C25 194.36 25 224.72 25 256C29.0465 252.763 31.8897 249.854 34.9375 245.812C39.1544 240.334 43.4654 234.95 47.875 229.625C49.0665 228.184 50.2579 226.742 51.4492 225.301C52.0388 224.587 52.6284 223.874 53.2358 223.139C60.5987 214.211 67.8623 205.212 74.9729 196.082C78.0953 192.094 81.3286 188.219 84.625 184.375C93.89 174.945 93.89 174.945 99 164C74.58 164 50.16 164 25 164ZM124 172C122.296 173.677 122.296 173.677 120.625 176C119.922 176.913 119.22 177.825 118.496 178.766C118.103 179.279 117.709 179.793 117.304 180.322C114.824 183.512 112.246 186.623 109.688 189.75C105.526 194.853 101.42 199.992 97.375 205.188C93.4114 210.274 89.3101 215.216 85.1179 220.115C81.4019 224.477 77.8597 228.957 74.3425 233.479C69.9929 239.054 65.4983 244.503 60.9922 249.951C57.2454 254.482 53.5376 259.039 49.8867 263.648C46.879 267.397 43.7807 271.072 40.6875 274.75C38.8 276.798 38.8 276.798 39 279C96.42 279 153.84 279 213 279C211.09 274.226 208.908 271.29 205.563 267.562C201.392 262.814 197.362 258.003 193.5 253C189.795 248.214 185.968 243.57 182 239C177.546 233.871 173.304 228.62 169.145 223.25C165.88 219.06 162.482 215.01 159 211C154.552 205.878 150.315 200.635 146.162 195.272C142.252 190.252 138.147 185.417 133.971 180.617C131.987 178.334 130.064 176.093 128.344 173.602C127.9 173.073 127.457 172.545 127 172C126.01 172 125.02 172 124 172Z'
        fill='#1F40ED'
      />
    </svg>
  )
}

export const NotionIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' width='1em' height='1em' {...props}>
      <path
        d='M31.494141 5.1503906L5.9277344 7.0019531A1.0001 1.0001 0 005.9042969 7.0039062A1.0001 1.0001 0 005.8652344 7.0097656A1.0001 1.0001 0 005.7929688 7.0214844A1.0001 1.0001 0 005.7636719 7.0292969A1.0001 1.0001 0 005.7304688 7.0371094A1.0001 1.0001 0 005.6582031 7.0605469A1.0001 1.0001 0 005.6113281 7.0800781A1.0001 1.0001 0 005.5839844 7.0917969A1.0001 1.0001 0 005.4335938 7.1777344A1.0001 1.0001 0 005.4082031 7.1933594A1.0001 1.0001 0 005.3476562 7.2421875A1.0001 1.0001 0 005.3359375 7.2539062A1.0001 1.0001 0 005.2871094 7.2988281A1.0001 1.0001 0 005.2578125 7.3320312A1.0001 1.0001 0 005.2148438 7.3828125A1.0001 1.0001 0 005.1992188 7.4023438A1.0001 1.0001 0 005.15625 7.4648438A1.0001 1.0001 0 005.1445312 7.484375A1.0001 1.0001 0 005.1074219 7.5488281A1.0001 1.0001 0 005.09375 7.5761719A1.0001 1.0001 0 005.0644531 7.6484375A1.0001 1.0001 0 005.0605469 7.65625A1.0001 1.0001 0 005.015625 7.8300781A1.0001 1.0001 0 005.0097656 7.8613281A1.0001 1.0001 0 005.0019531 7.9414062A1.0001 1.0001 0 005.0019531 7.9453125A1.0001 1.0001 0 005 8L5 33.738281C5 34.76391 5.3151542 35.766862 5.9042969 36.607422A1.0001 1.0001 0 005.953125 36.671875L12.126953 44.101562A1.0001 1.0001 0 0012.359375 44.382812L12.75 44.851562A1.0006635 1.0006635 0 0012.917969 45.011719C13.50508 45.581386 14.317167 45.917563 15.193359 45.861328L42.193359 44.119141C43.762433 44.017718 45 42.697027 45 41.125L45 15.132812C45 14.209354 44.565523 13.390672 43.904297 12.839844A1.0008168 1.0008168 0 0043.748047 12.695312L43.263672 12.337891A1.0001 1.0001 0 0043.0625 12.189453L34.824219 6.1132812C33.865071 5.4054876 32.682705 5.0641541 31.494141 5.1503906zM31.638672 7.1445312C32.352108 7.0927682 33.061867 7.29845 33.636719 7.7226562L39.767578 12.246094L14.742188 13.884766C13.880567 13.941006 13.037689 13.622196 12.425781 13.011719L12.423828 13.011719L8.2539062 8.8398438L31.638672 7.1445312zM7 10.414062L11.011719 14.425781L12 15.414062L12 40.818359L7.5390625 35.449219C7.1899317 34.947488 7 34.351269 7 33.738281L7 10.414062zM41.935547 14.134766C42.526748 14.096822 43 14.54116 43 15.132812L43 41.125C43 41.660973 42.59938 42.08847 42.064453 42.123047L15.064453 43.865234C14.770856 43.884078 14.506356 43.783483 14.314453 43.605469A1.0006635 1.0006635 0 0014.3125 43.603516C14.3125 43.603516 14.310547 43.601562 14.310547 43.601562C14.306465 43.597733 14.304796 43.59179 14.300781 43.587891A1.0006635 1.0006635 0 0014.289062 43.572266C14.112238 43.393435 14 43.149431 14 42.867188L14 16.875C14 16.337536 14.39999 15.911571 14.935547 15.876953L41.935547 14.134766zM38.496094 19L33.421875 19.28125C32.647875 19.36125 31.746094 19.938 31.746094 20.875L33.996094 21.0625L33.996094 31.753906L26.214844 19.751953L20.382812 20.080078C19.291812 20.160078 18.994141 20.970953 18.994141 22.001953L21.244141 22.001953L21.244141 37.566406C21.244141 37.566406 20.191844 37.850406 19.839844 37.941406C19.091844 38.134406 18.994141 38.784906 18.994141 39.253906C18.994141 39.253906 22.746656 39.065547 24.472656 38.935547C26.431656 38.785547 26.496094 37.472656 26.496094 37.472656L24.246094 37.003906L24.246094 25.470703C24.246094 25.470703 29.965844 34.660328 31.714844 37.361328C32.537844 38.630328 33.152375 38.878906 34.234375 38.878906C35.122375 38.878906 35.962141 38.616594 36.994141 38.058594L36.994141 20.697266C36.994141 20.697266 37.184203 20.687141 37.783203 20.494141C38.466203 20.273141 38.496094 19.656 38.496094 19z'
        fill='currentColor'
      />
    </svg>
  )
}

export const PerplexityIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg width='1em' height='1em' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M19.785 0v7.272H22.5V17.62h-2.935V24l-7.037-6.194v6.145h-1.091v-6.152L4.392 24v-6.465H1.5V7.188h2.884V0l7.053 6.494V.19h1.09v6.49L19.786 0zm-7.257 9.044v7.319l5.946 5.234V14.44l-5.946-5.397zm-1.099-.08l-5.946 5.398v7.235l5.946-5.234V8.965zm8.136 7.58h1.844V8.349H13.46l6.105 5.54v2.655zm-8.982-8.28H2.59v8.195h1.8v-2.576l6.192-5.62zM5.475 2.476v4.71h5.115l-5.115-4.71zm13.219 0l-5.115 4.71h5.115v-4.71z'
        fill='#20808D'
        fillRule='nonzero'
      />
    </svg>
  )
}

export const xIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' width='1em' height='1em' {...props}>
      <path
        d='M 5.9199219 6 L 20.582031 27.375 L 6.2304688 44 L 9.4101562 44 L 21.986328 29.421875 L 31.986328 44 L 44 44 L 28.681641 21.669922 L 42.199219 6 L 39.029297 6 L 27.275391 19.617188 L 17.933594 6 L 5.9199219 6 z M 9.7167969 8 L 16.880859 8 L 40.203125 42 L 33.039062 42 L 9.7167969 8 z'
        fill='currentColor'
      />
    </svg>
  )
}

export const SlackIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg' {...props}>
    <g>
      <path
        d='M53.8412698,161.320635 C53.8412698,176.152381 41.8539683,188.139683 27.0222222,188.139683 C12.1904762,188.139683 0.203174603,176.152381 0.203174603,161.320635 C0.203174603,146.488889 12.1904762,134.501587 27.0222222,134.501587 L53.8412698,134.501587 L53.8412698,161.320635 Z M67.2507937,161.320635 C67.2507937,146.488889 79.2380952,134.501587 94.0698413,134.501587 C108.901587,134.501587 120.888889,146.488889 120.888889,161.320635 L120.888889,228.368254 C120.888889,243.2 108.901587,255.187302 94.0698413,255.187302 C79.2380952,255.187302 67.2507937,243.2 67.2507937,228.368254 L67.2507937,161.320635 Z'
        fill='#E01E5A'
      />
      <path
        d='M94.0698413,53.6380952 C79.2380952,53.6380952 67.2507937,41.6507937 67.2507937,26.8190476 C67.2507937,11.9873016 79.2380952,-7.10542736e-15 94.0698413,-7.10542736e-15 C108.901587,-7.10542736e-15 120.888889,11.9873016 120.888889,26.8190476 L120.888889,53.6380952 L94.0698413,53.6380952 Z M94.0698413,67.2507937 C108.901587,67.2507937 120.888889,79.2380952 120.888889,94.0698413 C120.888889,108.901587 108.901587,120.888889 94.0698413,120.888889 L26.8190476,120.888889 C11.9873016,120.888889 0,108.901587 0,94.0698413 C0,79.2380952 11.9873016,67.2507937 26.8190476,67.2507937 L94.0698413,67.2507937 Z'
        fill='#36C5F0'
      />
      <path
        d='M201.549206,94.0698413 C201.549206,79.2380952 213.536508,67.2507937 228.368254,67.2507937 C243.2,67.2507937 255.187302,79.2380952 255.187302,94.0698413 C255.187302,108.901587 243.2,120.888889 228.368254,120.888889 L201.549206,120.888889 L201.549206,94.0698413 Z M188.139683,94.0698413 C188.139683,108.901587 176.152381,120.888889 161.320635,120.888889 C146.488889,120.888889 134.501587,108.901587 134.501587,94.0698413 L134.501587,26.8190476 C134.501587,11.9873016 146.488889,-1.42108547e-14 161.320635,-1.42108547e-14 C176.152381,-1.42108547e-14 188.139683,11.9873016 188.139683,26.8190476 L188.139683,94.0698413 Z'
        fill='#2EB67D'
      />
      <path
        d='M161.320635,201.549206 C176.152381,201.549206 188.139683,213.536508 188.139683,228.368254 C188.139683,243.2 176.152381,255.187302 161.320635,255.187302 C146.488889,255.187302 134.501587,243.2 134.501587,228.368254 L134.501587,201.549206 L161.320635,201.549206 Z M161.320635,188.139683 C146.488889,188.139683 134.501587,176.152381 134.501587,161.320635 C134.501587,146.488889 146.488889,134.501587 161.320635,134.501587 L228.571429,134.501587 C243.403175,134.501587 255.390476,146.488889 255.390476,161.320635 C255.390476,176.152381 243.403175,188.139683 228.571429,188.139683 L161.320635,188.139683 Z'
        fill='#ECB22E'
      />
    </g>
  </svg>
)

export const ResponseIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor' {...props}>
    <path d='M20 18v-2a4 4 0 0 0-4-4H4' />
    <path d='m9 17-5-5 5-5' />
  </svg>
)

export const StarterIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
    <path d='M8 5v14l11-7z' fill='currentColor' />
  </svg>
)

export const LoopIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
    <path
      d='M4 12a8 8 0 018-8V2.5L16 6l-4 3.5V8a6 6 0 00-6 6 6 6 0 006 6 6 6 0 006-6h2a8 8 0 01-8 8 8 8 0 01-8-8z'
      fill='currentColor'
    />
  </svg>
)

export const ParallelIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
    <rect x='3' y='3' width='18' height='6' rx='1' stroke='currentColor' strokeWidth='2' />
    <rect x='3' y='15' width='18' height='6' rx='1' stroke='currentColor' strokeWidth='2' />
    <path d='M12 9v6' stroke='currentColor' strokeWidth='2' strokeLinecap='round' />
  </svg>
)
