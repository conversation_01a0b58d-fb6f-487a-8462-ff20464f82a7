---
title: Execution
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Card, Cards } from 'fumadocs-ui/components/card'

Sim's execution engine brings your workflows to life by processing blocks in the correct order, managing data flow, and handling errors gracefully, so you can understand exactly how workflows are executed in Sim.

<Callout type="info">
  Every workflow execution follows a deterministic path based on your block connections and logic, ensuring predictable and reliable results.
</Callout>

## Documentation Overview

<Cards>
  <Card title="Execution Basics" href="/execution/basics">
    Learn about the fundamental execution flow, block types, and how data flows through your
    workflow
  </Card>

  <Card title="Logging" href="/execution/logging">
    Monitor workflow executions with comprehensive logging and real-time visibility
  </Card>
  
  <Card title="Cost Calculation" href="/execution/costs">
    Understand how workflow execution costs are calculated and optimized
  </Card>
  
  <Card title="External API" href="/execution/api">
    Access execution logs and set up webhooks programmatically via REST API
  </Card>
</Cards>

## Key Concepts

### Topological Execution
Blocks execute in dependency order, similar to how a spreadsheet recalculates cells. The execution engine automatically determines which blocks can run based on completed dependencies.

### Path Tracking
The engine actively tracks execution paths through your workflow. Router and Condition blocks dynamically update these paths, ensuring only relevant blocks execute.

### Layer-Based Processing
Instead of executing blocks one-by-one, the engine identifies layers of blocks that can run in parallel, optimizing performance for complex workflows.

### Execution Context
Each workflow maintains a rich context during execution containing:
- Block outputs and states
- Active execution paths
- Loop and parallel iteration tracking
- Environment variables
- Routing decisions

## Execution Triggers

Workflows can be executed through multiple channels:

- **Manual**: Test and debug directly in the editor
- **Deploy as API**: Create an HTTP endpoint secured with API keys
- **Deploy as Chat**: Create a conversational interface on a custom subdomain
- **Webhooks**: Respond to external events from third-party services
- **Scheduled**: Run on a recurring schedule using cron expressions

### Deploy as API

When you deploy a workflow as an API, Sim:
- Creates a unique HTTP endpoint: `https://sim.ai/api/workflows/{workflowId}/execute`
- Generates an API key for authentication
- Accepts POST requests with JSON payloads
- Returns workflow execution results as JSON

Example API call:
```bash
curl -X POST https://sim.ai/api/workflows/your-workflow-id/execute \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"input": "your data here"}'
```

### Deploy as Chat

Chat deployment creates a conversational interface for your workflow:
- Hosted on a custom subdomain: `https://your-name.sim.ai`
- Optional authentication (public, password, or email-based)
- Customizable UI with your branding
- Streaming responses for real-time interaction
- Perfect for AI assistants, support bots, or interactive tools

Each deployment method passes data to your workflow's starter block, beginning the execution flow.

## Programmatic Execution

Execute workflows from your applications using our official SDKs:

```bash
# TypeScript/JavaScript
npm install simstudio-ts-sdk

# Python
pip install simstudio-sdk
```

```typescript
// TypeScript Example
import { SimStudioClient } from 'simstudio-ts-sdk';

const client = new SimStudioClient({ 
  apiKey: 'your-api-key' 
});

const result = await client.executeWorkflow('workflow-id', {
  input: { message: 'Hello' }
});
```

## Best Practices

### Design for Reliability
- Handle errors gracefully with appropriate fallback paths
- Use environment variables for sensitive data
- Add logging to Function blocks for debugging

### Optimize Performance
- Minimize external API calls where possible
- Use parallel execution for independent operations
- Cache results with Memory blocks when appropriate

### Monitor Executions
- Review logs regularly to understand performance patterns
- Track costs for AI model usage
- Use workflow snapshots to debug issues

## What's Next?

Start with [Execution Basics](/execution/basics) to understand how workflows run, then explore [Logging](/execution/logging) to monitor your executions and [Cost Calculation](/execution/costs) to optimize your spending.
