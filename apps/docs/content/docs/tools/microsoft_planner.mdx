---
title: Microsoft Planner
description: Read and create tasks in Microsoft Planner
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="microsoft_planner"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"  fill='currentColor' viewBox='-1 -1 27 27' xmlns='http://www.w3.org/2000/svg'>
      <defs>
        <linearGradient
          id='paint0_linear_3984_11038'
          x1='6.38724'
          y1='3.74167'
          x2='2.15779'
          y2='12.777'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#8752E0' />
          <stop offset='1' stopColor='#541278' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_3984_11038'
          x1='8.38032'
          y1='11.0696'
          x2='4.94062'
          y2='7.69244'
          gradientUnits='userSpaceOnUse'
        >
          <stop offset='0.12172' stopColor='#3D0D59' />
          <stop offset='1' stopColor='#7034B0' stopOpacity='0' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='1' stopColor='#6C0F71' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_3984_11038'
          x1='18.3701'
          y1='-3.33385e-05'
          x2='9.85717'
          y2='20.4192'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#DB45E0' />
          <stop offset='0.677403' stopColor='#A829AE' />
          <stop offset='1' stopColor='#8F28B3' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_3984_11038'
          x1='18.0002'
          y1='7.49958'
          x2='14.0004'
          y2='23.9988'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#00479E' />
        </linearGradient>
        <linearGradient
          id='paint5_linear_3984_11038'
          x1='18.2164'
          y1='7.92626'
          x2='10.5237'
          y2='22.9363'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#3DCBFF' />
          <stop offset='1' stopColor='#4A40D4' />
        </linearGradient>
      </defs>
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint0_linear_3984_11038)'
      />
      <path
        d='M8.25809 15.7412C7.22488 16.7744 5.54971 16.7744 4.5165 15.7412L0.774909 11.9996C-0.258303 10.9664 -0.258303 9.29129 0.774908 8.25809L4.5165 4.51655C5.54971 3.48335 7.22488 3.48335 8.25809 4.51655L11.9997 8.2581C13.0329 9.29129 13.0329 10.9664 11.9997 11.9996L8.25809 15.7412Z'
        fill='url(#paint1_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint2_linear_3984_11038)'
      />
      <path
        d='M0.774857 11.9999C1.80809 13.0331 3.48331 13.0331 4.51655 11.9999L15.7417 0.774926C16.7749 -0.258304 18.4501 -0.258309 19.4834 0.774914L23.225 4.51655C24.2583 5.54977 24.2583 7.22496 23.225 8.25819L11.9999 19.4832C10.9667 20.5164 9.29146 20.5164 8.25822 19.4832L0.774857 11.9999Z'
        fill='url(#paint3_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint4_linear_3984_11038)'
      />
      <path
        d='M4.51642 15.7413C5.54966 16.7746 7.22487 16.7746 8.25812 15.7413L15.7415 8.25803C16.7748 7.2248 18.45 7.2248 19.4832 8.25803L23.2249 11.9997C24.2582 13.0329 24.2582 14.7081 23.2249 15.7413L15.7415 23.2246C14.7083 24.2579 13.033 24.2579 11.9998 23.2246L4.51642 15.7413Z'
        fill='url(#paint5_linear_3984_11038)'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Microsoft Planner](https://www.microsoft.com/en-us/microsoft-365/planner) is a task management tool that helps teams organize work visually using boards, tasks, and buckets. Integrated with Microsoft 365, it offers a simple, intuitive way to manage team projects, assign responsibilities, and track progress.

With Microsoft Planner, you can:

- **Create and manage tasks**: Add new tasks with due dates, priorities, and assigned users
- **Organize with buckets**: Group tasks by phase, status, or category to reflect your team’s workflow
- **Visualize project status**: Use boards, charts, and filters to monitor workload and track progress
- **Stay integrated with Microsoft 365**: Seamlessly connect tasks with Teams, Outlook, and other Microsoft tools

In Sim, the Microsoft Planner integration allows your agents to programmatically create, read, and manage tasks as part of their workflows. Agents can generate new tasks based on incoming requests, retrieve task details to drive decisions, and track status across projects — all without human intervention. Whether you're building workflows for client onboarding, internal project tracking, or follow-up task generation, integrating Microsoft Planner with Sim gives your agents a structured way to coordinate work, automate task creation, and keep teams aligned.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Integrate Microsoft Planner functionality to manage tasks. Read all user tasks, tasks from specific plans, individual tasks, or create new tasks with various properties like title, description, due date, and assignees using OAuth authentication.



## Tools

### `microsoft_planner_read_task`

Read tasks from Microsoft Planner - get all user tasks or all tasks from a specific plan

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `planId` | string | No | The ID of the plan to get tasks from \(if not provided, gets all user tasks\) |
| `taskId` | string | No | The ID of the task to get |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Whether tasks were retrieved successfully |
| `tasks` | array | Array of task objects with filtered properties |
| `metadata` | object | Metadata including planId, userId, and planUrl |

### `microsoft_planner_create_task`

Create a new task in Microsoft Planner

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `planId` | string | Yes | The ID of the plan where the task will be created |
| `title` | string | Yes | The title of the task |
| `description` | string | No | The description of the task |
| `dueDateTime` | string | No | The due date and time for the task \(ISO 8601 format\) |
| `assigneeUserId` | string | No | The user ID to assign the task to |
| `bucketId` | string | No | The bucket ID to place the task in |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `success` | boolean | Whether the task was created successfully |
| `task` | object | The created task object with all properties |
| `metadata` | object | Metadata including planId, taskId, and taskUrl |



## Notes

- Category: `tools`
- Type: `microsoft_planner`
