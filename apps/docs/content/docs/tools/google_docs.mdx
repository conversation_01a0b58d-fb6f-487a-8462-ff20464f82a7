---
title: Google Docs
description: Read, write, and create documents
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="google_docs"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 48 48'
      
      
    >
      <path
        fill='#2196f3'
        d='M37,45H11c-1.657,0-3-1.343-3-3V6c0-1.657,1.343-3,3-3h19l10,10v29C40,43.657,38.657,45,37,45z'
      />
      <path fill='#bbdefb' d='M40 13L30 13 30 3z' />
      <path fill='#1565c0' d='M30 13L40 23 40 13z' />
      <path fill='#e3f2fd' d='M15 23H33V25H15zM15 27H33V29H15zM15 31H33V33H15zM15 35H25V37H15z' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Google Docs](https://docs.google.com) is a powerful cloud-based document creation and editing service that allows users to create, edit, and collaborate on documents in real-time. As part of Google's productivity suite, Google Docs offers a versatile platform for text documents with robust formatting, commenting, and sharing capabilities.

Learn how to integrate the Google Docs "Read" tool in Sim to effortlessly fetch data from your docs and to integrate into your workflows. This tutorial walks you through connecting Google Docs, setting up data reads, and using that information to automate processes in real-time. Perfect for syncing live data with your agents.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/f41gy9rBHhE"
  title="Use the Google Docs Read tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Learn how to integrate the Google Docs "Update" tool in Sim to effortlessly add content in your docs through your workflows. This tutorial walks you through connecting Google Docs, configuring data writes, and using that information to automate document updates seamlessly. Perfect for maintaining dynamic, real-time documentation with minimal effort.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/L64ROHS2ivA"
  title="Use the Google Docs Update tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

Learn how to integrate the Google Docs "Create" tool in Sim to effortlessly generate new documents through your workflows. This tutorial walks you through connecting Google Docs, setting up document creation, and using workflow data to populate content automatically. Perfect for streamlining document generation and enhancing productivity.

<iframe
  width="100%"
  height="400"
  src="https://www.youtube.com/embed/lWpHH4qddWk"
  title="Use the Google Docs Create tool in Sim"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

With Google Docs, you can:

- **Create and edit documents**: Develop text documents with comprehensive formatting options
- **Collaborate in real-time**: Work simultaneously with multiple users on the same document
- **Track changes**: View revision history and restore previous versions
- **Comment and suggest**: Provide feedback and propose edits without changing the original content
- **Access anywhere**: Use Google Docs across devices with automatic cloud synchronization
- **Work offline**: Continue working without internet connection with changes syncing when back online
- **Integrate with other services**: Connect with Google Drive, Sheets, Slides, and third-party applications

In Sim, the Google Docs integration enables your agents to interact directly with document content programmatically. This allows for powerful automation scenarios such as document creation, content extraction, collaborative editing, and document management. Your agents can read existing documents to extract information, write to documents to update content, and create new documents from scratch. This integration bridges the gap between your AI workflows and document management, enabling seamless interaction with one of the world's most widely used document platforms. By connecting Sim with Google Docs, you can automate document workflows, generate reports, extract insights from documents, and maintain documentation - all through your intelligent agents.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Integrate Google Docs functionality to manage documents. Read content from existing documents, write to documents, and create new documents using OAuth authentication. Supports text content manipulation for document creation and editing.



## Tools

### `google_docs_read`

Read content from a Google Docs document

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `documentId` | string | Yes | The ID of the document to read |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Extracted document text content |
| `metadata` | json | Document metadata including ID, title, and URL |

### `google_docs_write`

Write or update content in a Google Docs document

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `documentId` | string | Yes | The ID of the document to write to |
| `content` | string | Yes | The content to write to the document |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `updatedContent` | boolean | Indicates if document content was updated successfully |
| `metadata` | json | Updated document metadata including ID, title, and URL |

### `google_docs_create`

Create a new Google Docs document

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `title` | string | Yes | The title of the document to create |
| `content` | string | No | The content of the document to create |
| `folderSelector` | string | No | Select the folder to create the document in |
| `folderId` | string | No | The ID of the folder to create the document in \(internal use\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `metadata` | json | Created document metadata including ID, title, and URL |



## Notes

- Category: `tools`
- Type: `google_docs`
