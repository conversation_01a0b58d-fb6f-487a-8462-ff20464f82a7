---
title: ElevenLabs
description: Convert TTS using ElevenLabs
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="elevenlabs"
  color="#181C1E"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 876 876'
      fill='none'
    >
      <path d='M498 138H618V738H498V138Z' fill='currentColor' />
      <path d='M258 138H378V738H258V138Z' fill='currentColor' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[ElevenLabs](https://elevenlabs.io/) is a state-of-the-art text-to-speech platform that creates incredibly natural and expressive AI voices. It offers some of the most realistic and emotionally nuanced synthetic voices available today, making it ideal for creating lifelike audio content.

With ElevenLabs, you can:

- **Generate natural-sounding speech**: Create audio that's nearly indistinguishable from human speech
- **Choose from diverse voice options**: Access a library of pre-made voices with different accents, tones, and characteristics
- **Clone voices**: Create custom voices based on audio samples (with proper permissions)
- **Control speech parameters**: Adjust stability, clarity, and emotional tone to fine-tune output
- **Add realistic emotions**: Incorporate natural-sounding emotions like happiness, sadness, or excitement

In Sim, the ElevenLabs integration enables your agents to convert text to lifelike speech, enhancing the interactivity and engagement of your applications. This is particularly valuable for creating voice assistants, generating audio content, developing accessible applications, or building conversational interfaces that feel more human. The integration allows you to seamlessly incorporate ElevenLabs' advanced speech synthesis capabilities into your agent workflows, bridging the gap between text-based AI and natural human communication.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Generate realistic speech from text using ElevenLabs voices.



## Tools

### `elevenlabs_tts`

Convert TTS using ElevenLabs voices

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `text` | string | Yes | The text to convert to speech |
| `voiceId` | string | Yes | The ID of the voice to use |
| `modelId` | string | No | The ID of the model to use \(defaults to eleven_monolingual_v1\) |
| `apiKey` | string | Yes | Your ElevenLabs API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `audioUrl` | string | The URL of the generated audio |



## Notes

- Category: `tools`
- Type: `elevenlabs`
