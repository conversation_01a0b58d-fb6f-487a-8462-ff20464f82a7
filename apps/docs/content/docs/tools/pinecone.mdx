---
title: Pinecone
description: Use Pinecone vector database
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="pinecone"
  color="#0D1117"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 256 288'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      preserveAspectRatio='xMidYMid'
    >
      <path
        d='M108.633615,254.43629 C117.713862,254.43629 125.074857,261.797284 125.074857,270.877532 C125.074857,279.957779 117.713862,287.318774 108.633615,287.318774 C99.5533677,287.318774 92.1923728,279.957779 92.1923728,270.877532 C92.1923728,261.797284 99.5533677,254.43629 108.633615,254.43629 Z M199.849665,224.438339 L216.09705,229.252379 L203.199913,272.780219 C202.072982,276.58361 198.458049,279.095992 194.500389,278.826397 L190.516677,278.552973 L190.419263,278.633409 L149.02918,275.728903 L150.180842,258.822508 L177.989056,260.709686 L159.783784,234.447622 L173.709616,224.792379 L191.938895,251.08702 L199.849665,224.438339 Z M23.0126771,194.347476 L39.9158866,195.544979 L37.935897,223.348728 L64.1501315,205.120082 L73.8271476,219.030793 L47.578736,237.278394 L74.3707554,245.173037 L69.5818063,261.427835 L25.8485266,248.543243 C22.0304448,247.418369 19.5101155,243.787479 19.7913963,239.817092 L23.0126771,194.347476 Z M132.151306,170.671396 L162.658679,207.503468 L148.909247,218.891886 L130.753266,196.972134 L124.866941,230.673893 L107.280249,227.599613 L113.172232,193.845272 L88.7296311,208.256891 L79.6674587,192.874434 L120.745504,168.674377 C124.522104,166.449492 129.355297,167.295726 132.151306,170.671396 Z M217.504528,145.960198 L232.744017,137.668804 L254.94482,178.473633 C256.889641,182.048192 256.088221,186.494171 253.017682,189.164674 L249.876622,191.878375 L217.826246,219.77131 L206.441034,206.680621 L227.988588,187.934494 L195.893546,182.152609 L198.972402,165.078949 L231.044844,170.857793 L217.504528,145.960198 Z M37.7821805,103.299272 L49.2622123,116.306888 L28.0106317,135.050179 L60.1668233,140.664193 L57.1863573,157.755303 L24.9947229,152.136967 L38.822104,177.134576 L23.6411026,185.532577 L1.08439616,144.756992 C-0.885025494,141.196884 -0.115545265,136.746375 2.93488097,134.054184 L37.7821805,103.299272 Z M146.476311,89.8796828 L176.88045,126.612847 L163.1271,137.996532 L144.975445,116.067101 L139.08912,149.778947 L121.502428,146.704666 L127.374238,113.081452 L103.025237,127.354817 L93.9976317,111.952048 L131.398812,90.0233663 L131.435631,89.880899 L131.600545,89.9023265 L135.085833,87.870141 C138.861877,85.6569913 143.68556,86.5079996 146.476311,89.8796828 Z M185.655786,71.8143168 L192.305535,55.7902703 L235.318239,73.6399229 C239.072486,75.1978811 241.2415,79.1537636 240.536356,83.1568091 L239.820231,87.1385839 L232.47517,128.919545 L215.389188,125.909819 L220.312646,97.9413879 L191.776157,113.7129 L183.390302,98.5251862 L211.981072,82.7408038 L185.655786,71.8143168 Z M103.71696,40.2373824 L104.456513,57.5706533 L76.0432671,58.785006 L97.4730368,83.2749086 L84.4165529,94.6993319 L62.9507932,70.1728358 L57.949673,98.1737132 L40.8716575,95.1191088 L49.0561498,49.3603563 C49.771444,45.3612115 53.1664633,42.3942036 57.2253811,42.2210231 L61.246149,42.0411642 L61.3363168,41.9758 L103.71696,40.2373824 Z M161.838155,3.27194826 L192.104824,40.2369789 L178.291207,51.5474574 L160.327329,29.6043227 L154.268381,63.2715157 L136.697231,60.1096121 L142.766468,26.3665075 L118.24002,40.7062765 L109.232678,25.2916494 L150.427675,1.21987397 C154.218286,-0.995121237 159.056796,-0.124957814 161.838155,3.27194826 Z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Pinecone](https://www.pinecone.io) is a vector database designed for building high-performance vector search applications. It enables efficient storage, management, and similarity search of high-dimensional vector embeddings, making it ideal for AI applications that require semantic search capabilities.

With Pinecone, you can:

- **Store vector embeddings**: Efficiently manage high-dimensional vectors at scale
- **Perform similarity search**: Find the most similar vectors to a query vector in milliseconds
- **Build semantic search**: Create search experiences based on meaning rather than keywords
- **Implement recommendation systems**: Generate personalized recommendations based on content similarity
- **Deploy machine learning models**: Operationalize ML models that rely on vector similarity
- **Scale seamlessly**: Handle billions of vectors with consistent performance
- **Maintain real-time indexes**: Update your vector database in real-time as new data arrives

In Sim, the Pinecone integration enables your agents to leverage vector search capabilities programmatically as part of their workflows. This allows for sophisticated automation scenarios that combine natural language processing with semantic search and retrieval. Your agents can generate embeddings from text, store these vectors in Pinecone indexes, and perform similarity searches to find the most relevant information. This integration bridges the gap between your AI workflows and vector search infrastructure, enabling more intelligent information retrieval based on semantic meaning rather than exact keyword matching. By connecting Sim with Pinecone, you can create agents that understand context, retrieve relevant information from large datasets, and deliver more accurate and personalized responses to users - all without requiring complex infrastructure management or specialized knowledge of vector databases.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Store, search, and retrieve vector embeddings using Pinecone's specialized vector database. Generate embeddings from text and perform semantic similarity searches with customizable filtering options.



## Tools

### `pinecone_generate_embeddings`

Generate embeddings from text using Pinecone

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `model` | string | Yes | Model to use for generating embeddings |
| `inputs` | array | Yes | Array of text inputs to generate embeddings for |
| `apiKey` | string | Yes | Pinecone API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `data` | array | Generated embeddings data with values and vector type |
| `model` | string | Model used for generating embeddings |
| `vector_type` | string | Type of vector generated \(dense/sparse\) |
| `usage` | object | Usage statistics for embeddings generation |

### `pinecone_upsert_text`

Insert or update text records in a Pinecone index

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `indexHost` | string | Yes | Full Pinecone index host URL |
| `namespace` | string | Yes | Namespace to upsert records into |
| `records` | array | Yes | Record or array of records to upsert, each containing _id, text, and optional metadata |
| `apiKey` | string | Yes | Pinecone API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `statusText` | string | Status of the upsert operation |
| `upsertedCount` | number | Number of records successfully upserted |

### `pinecone_search_text`

Search for similar text in a Pinecone index

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `indexHost` | string | Yes | Full Pinecone index host URL |
| `namespace` | string | No | Namespace to search in |
| `searchQuery` | string | Yes | Text to search for |
| `topK` | string | No | Number of results to return |
| `fields` | array | No | Fields to return in the results |
| `filter` | object | No | Filter to apply to the search |
| `rerank` | object | No | Reranking parameters |
| `apiKey` | string | Yes | Pinecone API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `matches` | array | Search results with ID, score, and metadata |

### `pinecone_search_vector`

Search for similar vectors in a Pinecone index

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `indexHost` | string | Yes | Full Pinecone index host URL |
| `namespace` | string | No | Namespace to search in |
| `vector` | array | Yes | Vector to search for |
| `topK` | number | No | Number of results to return |
| `filter` | object | No | Filter to apply to the search |
| `includeValues` | boolean | No | Include vector values in response |
| `includeMetadata` | boolean | No | Include metadata in response |
| `apiKey` | string | Yes | Pinecone API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `matches` | array | Vector search results with ID, score, values, and metadata |
| `namespace` | string | Namespace where the search was performed |

### `pinecone_fetch`

Fetch vectors by ID from a Pinecone index

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `indexHost` | string | Yes | Full Pinecone index host URL |
| `ids` | array | Yes | Array of vector IDs to fetch |
| `namespace` | string | No | Namespace to fetch vectors from |
| `apiKey` | string | Yes | Pinecone API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `matches` | array | Fetched vectors with ID, values, metadata, and score |



## Notes

- Category: `tools`
- Type: `pinecone`
