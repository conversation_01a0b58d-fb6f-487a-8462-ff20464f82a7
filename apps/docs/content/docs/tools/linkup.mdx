---
title: Linkup
description: Search the web with Linkup
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="linkup"
  color="#D6D3C7"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      xmlns='http://www.w3.org/2000/svg'
      viewBox='0 0 24 24'
      
      
      fill='none'
    >
      <path
        d='M20.2 14.1c-.4-.3-1.6-.4-2.9-.2.5-1.4 1.3-3.9.1-5-.6-.5-1.5-.7-2.6-.5-.3 0-.6.1-1 .2-1.1-1.6-2.4-2.5-3.8-2.5-1.6 0-3.1 1-4.1 2.9-1.2 2.1-1.9 5.1-1.9 8.8v.03l.4.3c3-.9 7.5-2.3 10.7-2.9 0 .9.1 1.9.1 2.8v.03l.4.3c.1 0 5.4-1.7 5.3-3.3 0-.2-.1-.5-.3-.7zM19.9 14.7c.03.4-1.7 1.4-4 2.3.5-.7 1-1.6 1.3-2.5 1.4-.1 2.4-.1 2.7.2zM16.4 14.6c-.3.7-.7 1.4-1.2 2-.02-.6-.1-1.2-.2-1.8.4-.1.9-.1 1.4-.2zM16.5 9.4c.8.7.9 2.4.1 5.1-.5.1-1 .1-1.5.2-.3-2-.9-3.8-1.7-5.3.3-.1.6-.2.8-.2.9-.1 1.7.05 2.3.2zM9.5 6.8c1.2 0 2.3.7 3.2 2.1-2.8 1.1-5.9 3.4-8.4 7.8.2-5.1 1.9-9.9 5.2-9.9zM4.7 17c3.4-4.9 6.4-6.8 8.4-7.8.7 1.3 1.2 2.9 1.5 4.8-3.2.6-7.3 1.8-9.9 3z'
        fill='currentColor'
        stroke='currentColor'
        strokeWidth='0.5'
        strokeLinejoin='round'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Linkup](https://linkup.so) is a powerful web search tool that integrates seamlessly with Sim, allowing your AI agents to access up-to-date information from the web with proper source attribution.

Linkup enhances your AI agents by providing them with the ability to search the web for current information. When integrated into your agent's toolkit:

- **Real-time Information Access**: Agents can retrieve the latest information from the web, keeping responses current and relevant.
- **Source Attribution**: All information comes with proper citations, ensuring transparency and credibility.
- **Simple Implementation**: Add Linkup to your agents toolset with minimal configuration.
- **Contextual Awareness**: Agents can use web information while maintaining their personality and conversational style.

To implement Linkup in your agent, simply add the tool to your agent's configuration. Your agent will then be able to search the web whenever they need to answer questions requiring current information.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Linkup Search allows you to search and retrieve up-to-date information from the web with source attribution.



## Tools

### `linkup_search`

Search the web for information using Linkup

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `q` | string | Yes | The search query |
| `depth` | string | Yes | Search depth \(has to either be "standard" or "deep"\) |
| `outputType` | string | Yes | Type of output to return \(has to either be "sourcedAnswer" or "searchResults"\) |
| `apiKey` | string | Yes | Enter your Linkup API key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `answer` | string | The sourced answer to the search query |
| `sources` | array | Array of sources used to compile the answer, each containing name, url, and snippet |



## Notes

- Category: `tools`
- Type: `linkup`
