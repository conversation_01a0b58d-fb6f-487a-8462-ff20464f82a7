---
title: Typeform
description: Interact with Typeform
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="typeform"
  color="#262627"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      version='1.1'
      id='Layer_1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      x='0px'
      y='0px'
      viewBox='0 0 122.3 80.3'
      xmlSpace='preserve'
    >
      <g>
        <path
          fill='currentColor'
          d='M94.3,0H65.4c-26,0-28,11.2-28,26.2l0,27.9c0,15.6,2,26.2,28.1,26.2h28.8c26,0,28-11.2,28-26.1V26.2
		C122.3,11.2,120.3,0,94.3,0z M0,20.1C0,6.9,5.2,0,14,0c8.8,0,14,6.9,14,20.1v40.1c0,13.2-5.2,20.1-14,20.1c-8.8,0-14-6.9-14-20.1
		V20.1z'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Typeform](https://www.typeform.com/) is a user-friendly platform for creating conversational forms, surveys, and quizzes with a focus on engaging user experience.

With Typeform, you can:

- **Create interactive forms**: Design beautiful, conversational forms that engage respondents with a unique one-question-at-a-time interface
- **Customize your experience**: Use conditional logic, hidden fields, and custom themes to create personalized user journeys
- **Integrate with other tools**: Connect with 1000+ apps through native integrations and APIs
- **Analyze response data**: Get actionable insights through comprehensive analytics and reporting tools

In Sim, the Typeform integration enables your agents to programmatically interact with your Typeform data as part of their workflows. Agents can retrieve form responses, process submission data, and incorporate user feedback directly into decision-making processes. This integration is particularly valuable for scenarios like lead qualification, customer feedback analysis, and data-driven personalization. By connecting Sim with Typeform, you can create intelligent automation workflows that transform form responses into actionable insights - analyzing sentiment, categorizing feedback, generating summaries, and even triggering follow-up actions based on specific response patterns.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Access and retrieve responses from your Typeform forms. Integrate form submissions data into your workflow for analysis, storage, or processing.



## Tools

### `typeform_responses`

Retrieve form responses from Typeform

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `formId` | string | Yes | Typeform form ID |
| `apiKey` | string | Yes | Typeform Personal Access Token |
| `pageSize` | number | No | Number of responses to retrieve \(default: 25\) |
| `since` | string | No | Retrieve responses submitted after this date \(ISO 8601 format\) |
| `until` | string | No | Retrieve responses submitted before this date \(ISO 8601 format\) |
| `completed` | string | No | Filter by completion status \(true/false\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `total_items` | number | Total response count |
| `page_count` | number | Total page count |
| `items` | json | Response items |

### `typeform_files`

Download files uploaded in Typeform responses

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `formId` | string | Yes | Typeform form ID |
| `responseId` | string | Yes | Response ID containing the files |
| `fieldId` | string | Yes | Unique ID of the file upload field |
| `filename` | string | Yes | Filename of the uploaded file |
| `inline` | boolean | No | Whether to request the file with inline Content-Disposition |
| `apiKey` | string | Yes | Typeform Personal Access Token |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `fileUrl` | string | Direct download URL for the uploaded file |
| `contentType` | string | MIME type of the uploaded file |
| `filename` | string | Original filename of the uploaded file |

### `typeform_insights`

Retrieve insights and analytics for Typeform forms

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `formId` | string | Yes | Typeform form ID |
| `apiKey` | string | Yes | Typeform Personal Access Token |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `fields` | array | Number of users who dropped off at this field |



## Notes

- Category: `tools`
- Type: `typeform`
