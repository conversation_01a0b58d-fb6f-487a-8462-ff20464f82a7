---
title: GitHub
description: Interact with GitHub or trigger workflows from GitHub events
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="github"
  color="#181C1E"
  icon={true}
  iconSvg={`<svg className="block-icon"    viewBox='0 0 26 26' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M13 0C11.2928 0 9.60235 0.336255 8.02511 0.989566C6.44788 1.64288 5.01477 2.60045 3.80761 3.80761C1.36964 6.24558 0 9.55219 0 13C0 18.746 3.731 23.621 8.892 25.35C9.542 25.454 9.75 25.051 9.75 24.7V22.503C6.149 23.283 5.382 20.761 5.382 20.761C4.784 19.253 3.939 18.85 3.939 18.85C2.756 18.044 4.03 18.07 4.03 18.07C5.33 18.161 6.019 19.409 6.019 19.409C7.15 21.385 9.061 20.8 9.802 20.488C9.919 19.643 10.257 19.071 10.621 18.746C7.735 18.421 4.706 17.303 4.706 12.35C4.706 10.907 5.2 9.75 6.045 8.827C5.915 8.502 5.46 7.15 6.175 5.395C6.175 5.395 7.267 5.044 9.75 6.721C10.777 6.435 11.895 6.292 13 6.292C14.105 6.292 15.223 6.435 16.25 6.721C18.733 5.044 19.825 5.395 19.825 5.395C20.54 7.15 20.085 8.502 19.955 8.827C20.8 9.75 21.294 10.907 21.294 12.35C21.294 17.316 18.252 18.408 15.353 18.733C15.821 19.136 16.25 19.929 16.25 21.138V24.7C16.25 25.051 16.458 25.467 17.121 25.35C22.282 23.608 26 18.746 26 13C26 11.2928 25.6637 9.60235 25.0104 8.02511C24.3571 6.44788 23.3995 5.01477 22.1924 3.80761C20.9852 2.60045 19.5521 1.64288 17.9749 0.989566C16.3977 0.336255 14.7072 0 13 0Z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[GitHub](https://github.com/) is the world's leading platform for software development and version control using Git. It provides a collaborative environment where developers can host and review code, manage projects, and build software together.

With GitHub, you can:

- **Host repositories**: Store your code in public or private repositories with version control
- **Collaborate on code**: Use pull requests to propose changes, review code, and merge contributions
- **Track issues**: Create, assign, and manage issues to organize work and track bugs
- **Automate workflows**: Use GitHub Actions to build, test, and deploy code automatically
- **Manage projects**: Organize work with project boards, milestones, and task tracking
- **Document code**: Create and maintain documentation with GitHub Pages and wikis

In Sim, the GitHub integration enables your agents to interact directly with GitHub repositories and workflows. This allows for powerful automation scenarios such as code review assistance, pull request management, issue tracking, and repository exploration. Your agents can fetch repository data, analyze code changes, post comments on pull requests, and perform other GitHub operations programmatically. This integration bridges the gap between your AI workflows and your development processes, enabling seamless collaboration between your agents and your development team.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Access GitHub repositories, pull requests, and comments through the GitHub API. Automate code reviews, PR management, and repository interactions within your workflow. Trigger workflows from GitHub events like push, pull requests, and issues.



## Tools

### `github_pr`

Fetch PR details including diff and files changed

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `owner` | string | Yes | Repository owner |
| `repo` | string | Yes | Repository name |
| `pullNumber` | number | Yes | Pull request number |
| `apiKey` | string | Yes | GitHub API token |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Human-readable PR summary |
| `metadata` | object | Detailed PR metadata including file changes |

### `github_comment`

Create comments on GitHub PRs

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `owner` | string | Yes | Repository owner |
| `repo` | string | Yes | Repository name |
| `body` | string | Yes | Comment content |
| `pullNumber` | number | Yes | Pull request number |
| `path` | string | No | File path for review comment |
| `position` | number | No | Line number for review comment |
| `commentType` | string | No | Type of comment \(pr_comment or file_comment\) |
| `line` | number | No | Line number for review comment |
| `side` | string | No | Side of the diff \(LEFT or RIGHT\) |
| `commitId` | string | No | The SHA of the commit to comment on |
| `apiKey` | string | Yes | GitHub API token |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Human-readable comment confirmation |
| `metadata` | object | Comment metadata |

### `github_repo_info`

Retrieve comprehensive GitHub repository metadata including stars, forks, issues, and primary language. Supports both public and private repositories with optional authentication.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `owner` | string | Yes | Repository owner \(user or organization\) |
| `repo` | string | Yes | Repository name |
| `apiKey` | string | Yes | GitHub Personal Access Token |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Human-readable repository summary |
| `metadata` | object | Repository metadata |

### `github_latest_commit`

Retrieve the latest commit from a GitHub repository

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `owner` | string | Yes | Repository owner \(user or organization\) |
| `repo` | string | Yes | Repository name |
| `branch` | string | No | Branch name \(defaults to the repository's default branch\) |
| `apiKey` | string | Yes | GitHub API token |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Human-readable commit summary |
| `metadata` | object | Commit metadata |



## Notes

- Category: `tools`
- Type: `github`
