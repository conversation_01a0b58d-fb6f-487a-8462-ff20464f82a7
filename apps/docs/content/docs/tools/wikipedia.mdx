---
title: Wikipedia
description: Search and retrieve content from Wikipedia
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="wikipedia"
  color="#000000"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      fill='currentColor'
      version='1.1'
      id='Capa_1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      
      
      viewBox='0 0 98.05 98.05'
      xmlSpace='preserve'
    >
      <g>
        <path
          d='M98.023,17.465l-19.584-0.056c-0.004,0.711-0.006,1.563-0.017,2.121c1.664,0.039,5.922,0.822,7.257,4.327L66.92,67.155
		c-0.919-2.149-9.643-21.528-10.639-24.02l9.072-18.818c1.873-2.863,5.455-4.709,8.918-4.843l-0.01-1.968L55.42,17.489
		c-0.045,0.499,0.001,1.548-0.068,2.069c5.315,0.144,7.215,1.334,5.941,4.508c-2.102,4.776-6.51,13.824-7.372,15.475
		c-2.696-5.635-4.41-9.972-7.345-16.064c-1.266-2.823,1.529-3.922,4.485-4.004v-1.981l-21.82-0.067
		c0.016,0.93-0.021,1.451-0.021,2.131c3.041,0.046,6.988,0.371,8.562,3.019c2.087,4.063,9.044,20.194,11.149,24.514
		c-2.685,5.153-9.207,17.341-11.544,21.913c-3.348-7.43-15.732-36.689-19.232-44.241c-1.304-3.218,3.732-5.077,6.646-5.213
		l0.019-2.148L0,17.398c0.005,0.646,0.027,1.71,0.029,2.187c4.025-0.037,9.908,6.573,11.588,10.683
		c7.244,16.811,14.719,33.524,21.928,50.349c0.002,0.029,2.256,0.059,2.281,0.008c4.717-9.653,10.229-19.797,15.206-29.56
		L63.588,80.64c0.005,0.004,2.082,0.016,2.093,0.007c7.962-18.196,19.892-46.118,23.794-54.933c1.588-3.767,4.245-6.064,8.543-6.194
		l0.032-1.956L98.023,17.465z'
        />
      </g>
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Wikipedia](https://www.wikipedia.org/) is the world's largest free online encyclopedia, offering millions of articles on a vast range of topics, collaboratively written and maintained by volunteers.

With Wikipedia, you can:

- **Search for articles**: Find relevant Wikipedia pages by searching for keywords or topics
- **Get article summaries**: Retrieve concise summaries of Wikipedia pages for quick reference
- **Access full content**: Obtain the complete content of Wikipedia articles for in-depth information
- **Discover random articles**: Explore new topics by retrieving random Wikipedia pages

In Sim, the Wikipedia integration enables your agents to programmatically access and interact with Wikipedia content as part of their workflows. Agents can search for articles, fetch summaries, retrieve full page content, and discover random articles, empowering your automations with up-to-date, reliable information from the world's largest encyclopedia. This integration is ideal for scenarios such as research, content enrichment, fact-checking, and knowledge discovery, allowing your agents to seamlessly incorporate Wikipedia data into their decision-making and task execution processes.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Access Wikipedia articles, search for pages, get summaries, retrieve full content, and discover random articles from the world's largest encyclopedia.



## Tools

### `wikipedia_summary`

Get a summary and metadata for a specific Wikipedia page.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `pageTitle` | string | Yes | Title of the Wikipedia page to get summary for |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `summary` | object | Wikipedia page summary and metadata |

### `wikipedia_search`

Search for Wikipedia pages by title or content.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | Search query to find Wikipedia pages |
| `searchLimit` | number | No | Maximum number of results to return \(default: 10, max: 50\) |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `searchResults` | array | Array of matching Wikipedia pages |

### `wikipedia_content`

Get the full HTML content of a Wikipedia page.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `pageTitle` | string | Yes | Title of the Wikipedia page to get content for |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | object | Full HTML content and metadata of the Wikipedia page |

### `wikipedia_random`

Get a random Wikipedia page.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `randomPage` | object | Random Wikipedia page data |



## Notes

- Category: `tools`
- Type: `wikipedia`
