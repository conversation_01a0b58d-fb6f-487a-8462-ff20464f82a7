---
title: Tavily
description: Search and extract information
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="tavily"
  color="#0066FF"
  icon={true}
  iconSvg={`<svg className="block-icon" viewBox='0 0 600 600' xmlns='http://www.w3.org/2000/svg' >
      <path
        d='M432 291C415 294 418 313 417 326C380 328 342 327 306 328C316 344 312 368 301 381C339 384 377 383 414 384C419 393 415 404 419 412C424 419 431 422 437 421C554 393 539 314 425 290'
        fill='rgb(248,202,81)'
      />
      <path
        d='M263 87C260 88 257 89 255 93C237 121 219 147 204 174C203 184 206 191 212 195C222 198 231 196 239 197C241 238 240 277 241 316C257 307 276 309 294 308C296 273 295 234 296 199C309 196 328 200 333 183C314 149 299 103 267 83'
        fill='rgb(109,164,249)'
      />
      <path
        d='M314 356L316 354C386 355 457 354 527 355C504 385 469 400 440 421C431 421 424 418 421 411C415 402 420 389 416 383C384 371 284 406 312 358'
        fill='rgb(250,188,28)'
      />
      <path
        d='M314 356C281 405 384 369 410 384C422 388 415 402 421 409C425 417 431 420 437 420C469 400 504 384 529 360C456 355 386 356 317 355'
        fill='rgb(251,186,23)'
      />
      <path
        d='M264 325C271 325 290 329 283 339C236 384 186 436 139 482C133 481 133 477 131 474C133 477 133 481 135 482C174 490 213 472 250 466C261 447 246 435 235 426C254 406 271 389 289 372C303 352 287 324 266 326'
        fill='rgb(251,156,158)'
      />
      <path
        d='M263 327C260 328 256 328 253 330C233 348 216 367 197 384C188 381 183 371 175 368C166 367 161 369 156 372C148 409 133 447 133 482C173 430 281 366 277 323'
        fill='rgb(248,56,63)'
      />
      <path
        d='M258 326C235 341 218 365 198 382C186 376 176 360 161 368L160 369L157 369C149 378 150 391 146 401C150 391 149 379 157 370C174 359 185 376 195 385C219 365 238 337 262 325'
        fill='rgb(242,165,165)'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Tavily](https://www.tavily.com/) is an AI-powered search API designed specifically for LLM applications. It provides reliable, real-time information retrieval capabilities with features optimized for AI use cases, including semantic search, content extraction, and structured data retrieval.

With Tavily, you can:

- **Perform contextual searches**: Get relevant results based on semantic understanding rather than just keyword matching
- **Extract structured content**: Pull specific information from web pages in a clean, usable format
- **Access real-time information**: Retrieve up-to-date data from across the web
- **Process multiple URLs simultaneously**: Extract content from several web pages in a single request
- **Receive AI-optimized results**: Get search results specifically formatted for consumption by AI systems

In Sim, the Tavily integration enables your agents to search the web and extract information as part of their workflows. This allows for sophisticated automation scenarios that require up-to-date information from the internet. Your agents can formulate search queries, retrieve relevant results, and extract content from specific web pages to inform their decision-making processes. This integration bridges the gap between your workflow automation and the vast knowledge available on the web, enabling your agents to access real-time information without manual intervention. By connecting Sim with Tavily, you can create agents that stay current with the latest information, provide more accurate responses, and deliver more value to users.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Access Tavily's AI-powered search engine to find relevant information from across the web. Extract and process content from specific URLs with customizable depth options.



## Tools

### `tavily_search`

Perform AI-powered web searches using Tavily

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | The search query to execute |
| `max_results` | number | No | Maximum number of results \(1-20\) |
| `apiKey` | string | Yes | Tavily API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `query` | string | The search query that was executed |
| `results` | array | results output from the tool |

### `tavily_extract`

Extract raw content from multiple web pages simultaneously using Tavily

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `urls` | string | Yes | URL or array of URLs to extract content from |
| `extract_depth` | string | No | The depth of extraction \(basic=1 credit/5 URLs, advanced=2 credits/5 URLs\) |
| `apiKey` | string | Yes | Tavily API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `results` | array | The URL that was extracted |



## Notes

- Category: `tools`
- Type: `tavily`
