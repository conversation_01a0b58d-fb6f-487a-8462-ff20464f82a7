---
title: Translate
description: Translate text to any language
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="translate"
  color="#FF4B4B"
  icon={true}
  iconSvg={`<svg className="block-icon"
      xmlns='http://www.w3.org/2000/svg'
      
      
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
      
    >
      <path d='m5 8 6 6' />
      <path d='m4 14 6-6 2-3' />
      <path d='M2 5h12' />
      <path d='M7 2h1' />
      <path d='m22 22-5-10-5 10' />
      <path d='M14 18h6' />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
Translate is a tool that allows you to translate text between languages.

With Translate, you can:

- **Translate text**: Translate text between languages
- **Translate documents**: Translate documents between languages
- **Translate websites**: Translate websites between languages
- **Translate images**: Translate images between languages
- **Translate audio**: Translate audio between languages
- **Translate videos**: Translate videos between languages
- **Translate speech**: Translate speech between languages
- **Translate text**: Translate text between languages
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Convert text between languages while preserving meaning, nuance, and formatting. Utilize powerful language models to produce natural, fluent translations with appropriate cultural adaptations.



## Tools

### `openai_chat`

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Translated text |
| `model` | string | Model used |
| `tokens` | json | Token usage |

### `anthropic_chat`


### `google_chat`

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `content` | string | Translated text |
| `model` | string | Model used |
| `tokens` | json | Token usage |



## Notes

- Category: `tools`
- Type: `translate`
