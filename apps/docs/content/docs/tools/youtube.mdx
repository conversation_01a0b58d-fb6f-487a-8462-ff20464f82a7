---
title: YouTube
description: Search for videos on YouTube
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="youtube"
  color="#FF0000"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      viewBox='0 0 28 20'
      fill='currentColor'
      xmlns='http://www.w3.org/2000/svg'
      
    >
      <path
        d='M11.2 14L18.466 9.8L11.2 5.6V14ZM27.384 3.038C27.566 3.696 27.692 4.578 27.776 5.698C27.874 6.818 27.916 7.784 27.916 8.624L28 9.8C28 12.866 27.776 15.12 27.384 16.562C27.034 17.822 26.222 18.634 24.962 18.984C24.304 19.166 23.1 19.292 21.252 19.376C19.432 19.474 17.766 19.516 16.226 19.516L14 19.6C8.134 19.6 4.48 19.376 3.038 18.984C1.778 18.634 0.966 17.822 0.616 16.562C0.434 15.904 0.308 15.022 0.224 13.902C0.126 12.782 0.0839999 11.816 0.0839999 10.976L0 9.8C0 6.734 0.224 4.48 0.616 3.038C0.966 1.778 1.778 0.966 3.038 0.616C3.696 0.434 4.9 0.308 6.748 0.224C8.568 0.126 10.234 0.0839999 11.774 0.0839999L14 0C19.866 0 23.52 0.224 24.962 0.616C26.222 0.966 27.034 1.778 27.384 3.038Z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[YouTube](https://www.youtube.com/) is the world's largest video sharing platform, hosting billions of videos and serving over 2 billion logged-in monthly users.

With YouTube's extensive API capabilities, you can:

- **Search content**: Find relevant videos across YouTube's vast library using specific keywords, filters, and parameters
- **Access metadata**: Retrieve detailed information about videos including titles, descriptions, view counts, and engagement metrics
- **Analyze trends**: Identify popular content and trending topics within specific categories or regions
- **Extract insights**: Gather data about audience preferences, content performance, and engagement patterns

In Sim, the YouTube integration enables your agents to programmatically search and analyze YouTube content as part of their workflows. This allows for powerful automation scenarios that require up-to-date video information. Your agents can search for instructional videos, research content trends, gather information from educational channels, or monitor specific creators for new uploads. This integration bridges the gap between your AI workflows and the world's largest video repository, enabling more sophisticated and content-aware automations. By connecting Sim with YouTube, you can create agents that stay current with the latest information, provide more accurate responses, and deliver more value to users - all without requiring manual intervention or custom code.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Find relevant videos on YouTube using the YouTube Data API. Search for content with customizable result limits and retrieve structured video metadata for integration into your workflow.



## Tools

### `youtube_search`

Search for videos on YouTube using the YouTube Data API.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | Search query for YouTube videos |
| `maxResults` | number | No | Maximum number of videos to return |
| `apiKey` | string | Yes | YouTube API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `items` | array | Array of YouTube videos matching the search query |



## Notes

- Category: `tools`
- Type: `youtube`
