---
title: Exa
description: Search with Exa AI
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="exa"
  color="#1F40ED"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 252 304'
      fill='none'
      role='img'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.82461 0.75046C5.94359 0.750786 5.94359 0.750786 7.08518 0.751118C7.94546 0.74652 8.80574 0.741921 9.69209 0.737183C10.6428 0.742326 11.5934 0.74747 12.5729 0.752769C13.5785 0.750355 14.584 0.74794 15.62 0.745453C19.0112 0.739788 22.4021 0.7485 25.7932 0.757087C28.2155 0.756015 30.6378 0.754077 33.0601 0.751335C38.9691 0.746979 44.878 0.751943 50.787 0.761185C57.6619 0.771553 64.5368 0.771499 71.4118 0.771013C83.674 0.770512 95.9362 0.779683 108.198 0.794197C120.11 0.808288 132.021 0.815367 143.932 0.814705C144.666 0.814665 145.399 0.814625 146.155 0.814584C146.888 0.814543 147.62 0.814503 148.375 0.814461C161.918 0.813772 175.461 0.819657 189.005 0.828614C193.802 0.831636 198.6 0.832695 203.397 0.833147C209.848 0.833898 216.298 0.840223 222.749 0.850313C225.124 0.853246 227.498 0.854462 229.873 0.853885C233.1 0.853398 236.327 0.859013 239.554 0.866295C240.507 0.864746 241.46 0.863197 242.442 0.861602C248.886 0.885829 248.886 0.885829 250 2.00001C252.1 17.5148 252.1 17.5148 247.828 23.7344C245.548 26.539 243.019 29.0818 240.419 31.5889C238.376 33.6204 236.637 35.8493 234.875 38.125C231.086 42.8997 227.239 47.6241 223.369 52.3335C222.383 53.5339 221.397 54.7348 220.412 55.936C214.429 63.2301 208.439 70.5132 202.313 77.6875C198.113 82.6097 194.1 87.6563 190.129 92.7637C186.095 97.9301 181.879 102.925 177.625 107.91C173.674 112.544 169.843 117.276 166 122C164.501 123.834 163.001 125.667 161.5 127.5C159.202 130.308 156.905 133.116 154.609 135.926C153.831 136.878 153.052 137.831 152.25 138.812C151.515 139.719 150.78 140.625 150.023 141.559C148.814 143.018 147.577 144.457 146.289 145.848C143.623 148.63 143.623 148.63 143.078 152.293C144.286 155.841 146.405 158.206 148.875 160.938C149.881 162.085 150.885 163.235 151.887 164.387C152.661 165.275 152.661 165.275 153.451 166.181C155.961 169.128 158.321 172.187 160.688 175.25C164.273 179.87 167.986 184.285 171.922 188.613C174.481 191.552 176.863 194.608 179.25 197.688C183.104 202.657 187.072 207.495 191.188 212.25C195.142 216.819 198.998 221.438 202.746 226.18C206.376 230.722 210.089 235.197 213.79 239.682C217.101 243.695 220.403 247.715 223.688 251.75C226.934 255.727 230.237 259.651 233.563 263.562C238.148 268.958 242.642 274.418 247 280C247.845 280.841 247.845 280.841 248.707 281.699C250.778 285.385 250.314 289.106 250.188 293.25C250.174 294.09 250.16 294.931 250.146 295.797C250.111 297.865 250.057 299.933 250 302C247.719 303.141 246.468 303.126 243.933 303.129C243.077 303.132 242.221 303.135 241.339 303.138C240.393 303.137 239.447 303.135 238.472 303.134C237.471 303.136 236.47 303.138 235.439 303.141C232.064 303.147 228.689 303.146 225.314 303.145C222.904 303.148 220.493 303.152 218.082 303.155C212.201 303.163 206.32 303.166 200.438 303.167C195.66 303.168 190.882 303.17 186.104 303.173C172.568 303.182 159.032 303.186 145.496 303.185C144.766 303.185 144.036 303.185 143.284 303.185C142.553 303.185 141.822 303.185 141.068 303.185C129.214 303.185 117.359 303.194 105.504 303.208C93.3424 303.223 81.1812 303.23 69.0199 303.229C62.1877 303.229 55.3555 303.231 48.5233 303.242C42.1015 303.252 35.6798 303.252 29.258 303.245C26.8948 303.243 24.5316 303.246 22.1685 303.252C18.955 303.26 15.7417 303.255 12.5282 303.247C11.5821 303.252 10.636 303.258 9.66125 303.263C8.80507 303.258 7.94889 303.254 7.06677 303.249C6.32436 303.249 5.58195 303.249 4.81705 303.25C3 303 3 303 1 301C0.749304 298.843 0.749304 298.843 0.74832 296.106C0.743105 295.065 0.737889 294.024 0.732515 292.952C0.736977 291.799 0.741439 290.647 0.746035 289.46C0.742915 288.243 0.739795 287.025 0.73658 285.771C0.729961 282.382 0.732374 278.993 0.738553 275.603C0.743062 271.952 0.736102 268.301 0.730593 264.65C0.721681 257.494 0.723629 250.337 0.729418 243.18C0.733928 237.366 0.734551 231.552 0.732371 225.738C0.732064 224.912 0.731758 224.085 0.731442 223.234C0.730803 221.556 0.730154 219.878 0.729497 218.2C0.723788 202.448 0.73036 186.696 0.741098 170.944C0.750034 157.416 0.748482 143.888 0.73926 130.36C0.728567 114.666 0.724337 98.9726 0.730485 83.2787C0.731122 81.6065 0.731751 79.9343 0.732371 78.2621C0.732834 77.0279 0.732834 77.0279 0.733306 75.7687C0.735036 69.9587 0.732112 64.1487 0.727412 58.3387C0.721814 51.2623 0.723297 44.1858 0.733973 37.1094C0.739244 33.496 0.741262 29.8827 0.734675 26.2694C0.727655 22.3596 0.736014 18.45 0.746035 14.5403C0.741574 13.388 0.737112 12.2357 0.732515 11.0484C0.737731 10.0076 0.742947 8.96683 0.74832 7.89449C0.748645 6.99108 0.748969 6.08767 0.749304 5.15689C1.07777 2.33093 1.9319 1.14609 4.82461 0.75046ZM39 25C40.5046 28.0092 41.6553 29.9923 43.7227 32.5039C44.2563 33.1562 44.79 33.8084 45.3398 34.4805C45.9083 35.1675 46.4768 35.8546 47.0625 36.5625C47.6503 37.2792 48.2381 37.9959 48.8438 38.7344C50.5594 40.8255 52.2795 42.9128 54 45C55.5008 46.8327 57.0009 48.6659 58.5 50.5C59.2038 51.3598 59.9077 52.2196 60.6328 53.1055C74.8482 70.4877 74.8482 70.4877 81.4375 78.9375C86.6547 85.6225 92.0434 92.1594 97.4453 98.6953C106.496 109.647 115.413 120.68 124 132C126.944 131.669 127.951 131.051 130.043 128.883C130.751 127.973 131.458 127.063 132.188 126.125C132.978 125.125 133.768 124.124 134.582 123.094C135.38 122.073 136.178 121.052 137 120C138.453 118.204 139.911 116.412 141.375 114.625C142.114 113.721 142.852 112.818 143.613 111.887C146.237 108.714 148.896 105.574 151.563 102.438C155.359 97.9705 159.069 93.4497 162.711 88.8555C167.531 82.8433 172.464 76.9234 177.368 70.98C181.651 65.7834 185.911 60.5683 190.16 55.3438C193.828 50.8413 197.525 46.3703 201.296 41.9539C209.025 34.2599 209.025 34.2599 213 25C155.58 25 98.16 25 39 25ZM25 48C25 78.36 25 108.72 25 140C49.42 140 73.84 140 99 140C97.1724 135.431 95.234 132.634 92.0625 129.062C88.1461 124.547 84.3496 119.974 80.6875 115.25C75.8962 109.087 70.9584 103.054 65.9834 97.0383C62.3144 92.6007 58.6856 88.1356 55.1016 83.6289C51.8102 79.5116 48.4818 75.4244 45.1528 71.3374C42.2047 67.7174 39.266 64.0903 36.3438 60.4492C35.7523 59.7146 35.1609 58.98 34.5515 58.2231C33.4108 56.8059 32.2728 55.3863 31.1379 53.9644C28.4954 50.4896 28.4954 50.4896 25 48ZM25 164C25 194.36 25 224.72 25 256C29.0465 252.763 31.8897 249.854 34.9375 245.812C39.1544 240.334 43.4654 234.95 47.875 229.625C49.0665 228.184 50.2579 226.742 51.4492 225.301C52.0388 224.587 52.6284 223.874 53.2358 223.139C60.5987 214.211 67.8623 205.212 74.9729 196.082C78.0953 192.094 81.3286 188.219 84.625 184.375C93.89 174.945 93.89 174.945 99 164C74.58 164 50.16 164 25 164ZM124 172C122.296 173.677 122.296 173.677 120.625 176C119.922 176.913 119.22 177.825 118.496 178.766C118.103 179.279 117.709 179.793 117.304 180.322C114.824 183.512 112.246 186.623 109.688 189.75C105.526 194.853 101.42 199.992 97.375 205.188C93.4114 210.274 89.3101 215.216 85.1179 220.115C81.4019 224.477 77.8597 228.957 74.3425 233.479C69.9929 239.054 65.4983 244.503 60.9922 249.951C57.2454 254.482 53.5376 259.039 49.8867 263.648C46.879 267.397 43.7807 271.072 40.6875 274.75C38.8 276.798 38.8 276.798 39 279C96.42 279 153.84 279 213 279C211.09 274.226 208.908 271.29 205.563 267.562C201.392 262.814 197.362 258.003 193.5 253C189.795 248.214 185.968 243.57 182 239C177.546 233.871 173.304 228.62 169.145 223.25C165.88 219.06 162.482 215.01 159 211C154.552 205.878 150.315 200.635 146.162 195.272C142.252 190.252 138.147 185.417 133.971 180.617C131.987 178.334 130.064 176.093 128.344 173.602C127.9 173.073 127.457 172.545 127 172C126.01 172 125.02 172 124 172Z'
        fill='currentColor'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Exa](https://exa.ai/) is an AI-powered search engine designed specifically for developers and researchers, providing highly relevant and up-to-date information from across the web. It combines advanced semantic search capabilities with AI understanding to deliver more accurate and contextually relevant results than traditional search engines.

With Exa, you can:

- **Search with natural language**: Find information using conversational queries and questions
- **Get precise results**: Receive highly relevant search results with semantic understanding
- **Access up-to-date information**: Retrieve current information from across the web
- **Find similar content**: Discover related resources based on content similarity
- **Extract webpage contents**: Retrieve and process the full text of web pages
- **Answer questions with citations**: Ask questions and receive direct answers with supporting sources
- **Perform research tasks**: Automate multi-step research workflows to gather, synthesize, and summarize information

In Sim, the Exa integration allows your agents to search the web for information, retrieve content from specific URLs, find similar resources, answer questions with citations, and conduct research tasks—all programmatically through API calls. This enables your agents to access real-time information from the internet, enhancing their ability to provide accurate, current, and relevant responses. The integration is particularly valuable for research tasks, information gathering, content discovery, and answering questions that require up-to-date information from across the web.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Search the web, retrieve content, find similar links, and answer questions using Exa's powerful AI search capabilities.



## Tools

### `exa_search`

Search the web using Exa AI. Returns relevant search results with titles, URLs, and text snippets.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | The search query to execute |
| `numResults` | number | No | Number of results to return \(default: 10, max: 25\) |
| `useAutoprompt` | boolean | No | Whether to use autoprompt to improve the query \(default: false\) |
| `type` | string | No | Search type: neural, keyword, auto or fast \(default: auto\) |
| `apiKey` | string | Yes | Exa AI API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `results` | array | Search results with titles, URLs, and text snippets |

### `exa_get_contents`

Retrieve the contents of webpages using Exa AI. Returns the title, text content, and optional summaries for each URL.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `urls` | string | Yes | Comma-separated list of URLs to retrieve content from |
| `text` | boolean | No | If true, returns full page text with default settings. If false, disables text return. |
| `summaryQuery` | string | No | Query to guide the summary generation |
| `apiKey` | string | Yes | Exa AI API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `results` | array | Retrieved content from URLs with title, text, and summaries |

### `exa_find_similar_links`

Find webpages similar to a given URL using Exa AI. Returns a list of similar links with titles and text snippets.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `url` | string | Yes | The URL to find similar links for |
| `numResults` | number | No | Number of similar links to return \(default: 10, max: 25\) |
| `text` | boolean | No | Whether to include the full text of the similar pages |
| `apiKey` | string | Yes | Exa AI API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `similarLinks` | array | Similar links found with titles, URLs, and text snippets |

### `exa_answer`

Get an AI-generated answer to a question with citations from the web using Exa AI.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | The question to answer |
| `text` | boolean | No | Whether to include the full text of the answer |
| `apiKey` | string | Yes | Exa AI API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `answer` | string | AI-generated answer to the question |
| `citations` | array | Sources and citations for the answer |

### `exa_research`

Perform comprehensive research using AI to generate detailed reports with citations

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | Yes | Research query or topic |
| `includeText` | boolean | No | Include full text content in results |
| `apiKey` | string | Yes | Exa AI API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `research` | array | Comprehensive research findings with citations and summaries |



## Notes

- Category: `tools`
- Type: `exa`
