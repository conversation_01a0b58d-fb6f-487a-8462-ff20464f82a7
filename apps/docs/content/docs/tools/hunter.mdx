---
title: Hunter io
description: Find and verify professional email addresses
---

import { BlockInfoCard } from "@/components/ui/block-info-card"

<BlockInfoCard 
  type="hunter"
  color="#E0E0E0"
  icon={true}
  iconSvg={`<svg className="block-icon"
      
      
      
      viewBox='0 0 20 19'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.0671 8.43455C11.6625 8.55094 11.2164 8.55288 10.7992 8.53525C10.3141 8.51472 9.80024 8.45339 9.35223 8.25426C8.98359 8.09047 8.68787 7.79493 8.84262 7.36805C8.95175 7.06699 9.19361 6.79803 9.47319 6.64644C9.78751 6.4759 10.1329 6.50361 10.4474 6.65774C10.8005 6.83082 11.0942 7.11235 11.3604 7.3964C11.5 7.54536 11.6332 7.70002 11.7646 7.85617C11.8252 7.92801 12.2364 8.33865 12.0671 8.43455ZM18.7923 8.58131C18.17 8.43655 17.4348 8.4884 16.811 8.38867C15.8284 8.23146 14.3648 7.08576 13.5714 5.92122C13.0201 5.11202 12.757 4.28785 12.3356 3.28356C12.0415 2.58257 11.4001 0.365389 10.5032 1.40318C10.1339 1.83057 9.7204 3.23752 9.41837 3.2177C9.19467 3.26971 9.15818 2.83371 9.08739 2.64738C8.95886 2.30903 8.89071 1.9176 8.7185 1.59854C8.58086 1.34353 8.40014 1.03806 8.12337 0.91412C7.63027 0.660572 7.03575 1.42476 6.74072 2.33095C6.61457 2.81687 5.76653 3.75879 5.39721 3.9866C3.71684 5.02352 0.344233 6.11595 0.000262184 9.75358C-0.00114142 9.76867 0.000262182 9.81455 0.0573714 9.77323C0.459591 9.48197 5.02183 6.19605 2.09392 12.5476C0.300195 16.439 8.96062 18.917 9.40582 18.9271C9.46582 18.9284 9.46144 18.9011 9.46347 18.8832C10.1546 12.6724 16.9819 13.3262 18.5718 11.8387C20.1474 10.3649 20.1796 8.93816 18.7923 8.58131Z'
        fill='#FA5320'
      />
    </svg>`}
/>

{/* MANUAL-CONTENT-START:intro */}
[Hunter.io](https://hunter.io/) is a leading platform for finding and verifying professional email addresses, discovering companies, and enriching contact data. Hunter.io provides robust APIs for domain search, email finding, verification, and company discovery, making it an essential tool for sales, recruiting, and business development.

With Hunter.io, you can:

- **Find email addresses by domain:** Search for all publicly available email addresses associated with a specific company domain.
- **Discover companies:** Use advanced filters and AI-powered search to find companies matching your criteria.
- **Find a specific email address:** Locate the most likely email address for a person at a company using their name and domain.
- **Verify email addresses:** Check the deliverability and validity of any email address.
- **Enrich company data:** Retrieve detailed information about companies, including size, technologies used, and more.

In Sim, the Hunter.io integration enables your agents to programmatically search for and verify email addresses, discover companies, and enrich contact data using Hunter.io’s API. This allows you to automate lead generation, contact enrichment, and email verification directly within your workflows. Your agents can leverage Hunter.io’s tools to streamline outreach, keep your CRM up-to-date, and power intelligent automation scenarios for sales, recruiting, and more.
{/* MANUAL-CONTENT-END */}


## Usage Instructions

Search for email addresses, verify their deliverability, discover companies, and enrich contact data using Hunter.io's powerful email finding capabilities.



## Tools

### `hunter_discover`

Returns companies matching a set of criteria using Hunter.io AI-powered search.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `query` | string | No | Natural language search query for companies |
| `domain` | string | No | Company domain names to filter by |
| `headcount` | string | No | Company size filter \(e.g., "1-10", "11-50"\) |
| `company_type` | string | No | Type of organization |
| `technology` | string | No | Technology used by companies |
| `apiKey` | string | Yes | Hunter.io API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `results` | array | Array of companies matching the search criteria, each containing domain, name, headcount, technologies, and email_count |

### `hunter_domain_search`

Returns all the email addresses found using one given domain name, with sources.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Yes | Domain name to search for email addresses |
| `limit` | number | No | Maximum email addresses to return \(default: 10\) |
| `offset` | number | No | Number of email addresses to skip |
| `type` | string | No | Filter for personal or generic emails |
| `seniority` | string | No | Filter by seniority level: junior, senior, or executive |
| `department` | string | No | Filter by specific departments \(e.g., sales, marketing\) |
| `apiKey` | string | Yes | Hunter.io API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `domain` | string | The searched domain name |
| `disposable` | boolean | Whether the domain accepts disposable email addresses |
| `webmail` | boolean | Whether the domain is a webmail provider |
| `accept_all` | boolean | Whether the domain accepts all email addresses |
| `pattern` | string | The email pattern used by the organization |
| `organization` | string | The organization name |
| `description` | string | Description of the organization |
| `industry` | string | Industry of the organization |
| `twitter` | string | Twitter profile of the organization |
| `facebook` | string | Facebook profile of the organization |
| `linkedin` | string | LinkedIn profile of the organization |
| `instagram` | string | Instagram profile of the organization |
| `youtube` | string | YouTube channel of the organization |
| `technologies` | array | Array of technologies used by the organization |
| `country` | string | Country where the organization is located |
| `state` | string | State where the organization is located |
| `city` | string | City where the organization is located |
| `postal_code` | string | Postal code of the organization |
| `street` | string | Street address of the organization |
| `emails` | array | Array of email addresses found for the domain, each containing value, type, confidence, sources, and person details |

### `hunter_email_finder`

Finds the most likely email address for a person given their name and company domain.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Yes | Company domain name |
| `first_name` | string | Yes | Person's first name |
| `last_name` | string | Yes | Person's last name |
| `company` | string | No | Company name |
| `apiKey` | string | Yes | Hunter.io API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `email` | string | The found email address |
| `score` | number | Confidence score for the found email address |
| `sources` | array | Array of sources where the email was found, each containing domain, uri, extracted_on, last_seen_on, and still_on_page |
| `verification` | object | Verification information containing date and status |

### `hunter_email_verifier`

Verifies the deliverability of an email address and provides detailed verification status.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `email` | string | Yes | The email address to verify |
| `apiKey` | string | Yes | Hunter.io API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `result` | string | Deliverability result: deliverable, undeliverable, or risky |
| `score` | number | Confidence score for the verification result |
| `email` | string | The verified email address |
| `regexp` | boolean | Whether the email follows a valid regex pattern |
| `gibberish` | boolean | Whether the email appears to be gibberish |
| `disposable` | boolean | Whether the email is from a disposable email provider |
| `webmail` | boolean | Whether the email is from a webmail provider |
| `mx_records` | boolean | Whether MX records exist for the domain |
| `smtp_server` | boolean | Whether the SMTP server is reachable |
| `smtp_check` | boolean | Whether the SMTP check was successful |
| `accept_all` | boolean | Whether the domain accepts all email addresses |
| `block` | boolean | Whether the email is blocked |
| `status` | string | Verification status: valid, invalid, accept_all, webmail, disposable, or unknown |
| `sources` | array | Array of sources where the email was found |

### `hunter_companies_find`

Enriches company data using domain name.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `domain` | string | Yes | Domain to find company data for |
| `apiKey` | string | Yes | Hunter.io API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `person` | object | Person information \(undefined for companies_find tool\) |
| `company` | object | Company information including name, domain, industry, size, country, linkedin, and twitter |

### `hunter_email_count`

Returns the total number of email addresses found for a domain or company.

#### Input

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| `domain` | string | No | Domain to count emails for \(required if company not provided\) |
| `company` | string | No | Company name to count emails for \(required if domain not provided\) |
| `type` | string | No | Filter for personal or generic emails only |
| `apiKey` | string | Yes | Hunter.io API Key |

#### Output

| Parameter | Type | Description |
| --------- | ---- | ----------- |
| `total` | number | Total number of email addresses found |
| `personal_emails` | number | Number of personal email addresses found |
| `generic_emails` | number | Number of generic email addresses found |
| `department` | object | Breakdown of email addresses by department \(executive, it, finance, management, sales, legal, support, hr, marketing, communication\) |
| `seniority` | object | Breakdown of email addresses by seniority level \(junior, senior, executive\) |



## Notes

- Category: `tools`
- Type: `hunter`
