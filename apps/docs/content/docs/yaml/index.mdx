---
title: YAML Workflow Reference
description: Complete guide to writing YAML workflows in Sim
---

import { Card, Cards } from "fumadocs-ui/components/card";
import { Step, Steps } from "fumadocs-ui/components/steps";
import { Tab, Tabs } from "fumadocs-ui/components/tabs";

YAML workflows provide a powerful way to define, version, and share workflow configurations in Sim. This reference guide covers the complete YAML syntax, block schemas, and best practices for creating robust workflows.

## Quick Start

Every Sim workflow follows this basic structure:

```yaml
version: '1.0'
blocks:
  start:
    type: starter
    name: Start
    inputs:
      startWorkflow: manual
    connections:
      success: agent-1

  agent-1:
    type: agent
    name: "AI Assistant"
    inputs:
      systemPrompt: "You are a helpful assistant."
      userPrompt: 'Hi'
      model: gpt-4o
      apiKey: '{{OPENAI_API_KEY}}'
```

## Core Concepts

<Steps>
  <Step>
    <strong>Version Declaration</strong>: Must be exactly `version: '1.0'` (with quotes)
  </Step>
  <Step>
    <strong>Blocks Structure</strong>: All workflow blocks are defined under the `blocks` key
  </Step>
  <Step>
    <strong>Block References</strong>: Use block names in lowercase with spaces removed (e.g., `<aiassistant.content>`)
  </Step>
  <Step>
    <strong>Environment Variables</strong>: Reference with double curly braces `{{VARIABLE_NAME}}`
  </Step>
</Steps>

## Block Types

Sim supports several core block types, each with specific YAML schemas:

<Cards>
  <Card title="Starter Block" href="/yaml/blocks/starter">
    Workflow entry point with support for manual, webhook, and scheduled triggers
  </Card>
  <Card title="Agent Block" href="/yaml/blocks/agent">
    AI-powered processing with support for tools and structured output
  </Card>
  <Card title="Function Block" href="/yaml/blocks/function">
    Custom JavaScript/TypeScript code execution
  </Card>
  <Card title="API Block" href="/yaml/blocks/api">
    HTTP requests to external services
  </Card>
  <Card title="Condition Block" href="/yaml/blocks/condition">
    Conditional branching based on boolean expressions
  </Card>
  <Card title="Router Block" href="/yaml/blocks/router">
    AI-powered intelligent routing to multiple paths
  </Card>
  <Card title="Loop Block" href="/yaml/blocks/loop">
    Iterative processing with for and forEach loops
  </Card>
  <Card title="Parallel Block" href="/yaml/blocks/parallel">
    Concurrent execution across multiple instances
  </Card>
  <Card title="Webhook Block" href="/yaml/blocks/webhook">
    Webhook triggers for external integrations
  </Card>
  <Card title="Evaluator Block" href="/yaml/blocks/evaluator">
    Validate outputs against defined criteria and metrics
  </Card>
  <Card title="Workflow Block" href="/yaml/blocks/workflow">
    Execute other workflows as reusable components
  </Card>
  <Card title="Response Block" href="/yaml/blocks/response">
    Final workflow output formatting
  </Card>
</Cards>

## Block Reference Syntax

The most critical aspect of YAML workflows is understanding how to reference data between blocks:

### Basic Rules

1. **Use the block name** (not the block ID) converted to lowercase with spaces removed
2. **Add the appropriate property** (.content for agents, .output for tools)
3. **When using chat, reference the starter block** as `<start.input>`

### Examples

```yaml
# Block definitions
email-processor:
  type: agent
  name: "Email Agent"
  # ... configuration

data-formatter:
  type: function
  name: "Data Agent"
  # ... configuration

# Referencing their outputs
next-block:
  type: agent
  name: "Next Step"
  inputs:
    userPrompt: |
      Process this email: <emailagent.content>
      Use this formatted data: <dataagent.output>
      Original input: <start.input>
```

### Special Cases

- **Loop Variables**: `<loop.index>`, `<loop.currentItem>`, `<loop.items>`
- **Parallel Variables**: `<parallel.index>`, `<parallel.currentItem>`

## Environment Variables

Use environment variables for sensitive data like API keys:

```yaml
inputs:
  apiKey: '{{OPENAI_API_KEY}}'
  database: '{{DATABASE_URL}}'
  token: '{{SLACK_BOT_TOKEN}}'
```

## Best Practices

- **Keep block names human-readable**: "Email Processor" for UI display
- **Reference environment variables**: Never hardcode API keys
- **Structure for readability**: Group related blocks logically
- **Test incrementally**: Build workflows step by step

## Next Steps

- [Block Reference Syntax](/yaml/block-reference) - Detailed reference rules
- [Complete Block Schemas](/yaml/blocks) - All available block types
- [Workflow Examples](/yaml/examples) - Real-world workflow patterns 