---
title: Webhook Block YAML Schema
description: YAML configuration reference for Webhook blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
properties:
  type:
    type: string
    enum: [webhook]
    description: Block type identifier
  name:
    type: string
    description: Display name for this webhook block
  inputs:
    type: object
    properties:
      webhookConfig:
        type: object
        description: Webhook configuration settings
        properties:
          enabled:
            type: boolean
            description: Whether the webhook is active
            default: true
          secret:
            type: string
            description: Secret key for webhook verification
          headers:
            type: array
            description: Expected headers for validation as table entries
            items:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the header entry
                key:
                  type: string
                  description: Header name
                value:
                  type: string
                  description: Expected header value
                cells:
                  type: object
                  description: Cell display values for the table interface
                  properties:
                    Key:
                      type: string
                      description: Display value for the key column
                    Value:
                      type: string
                      description: Display value for the value column
          methods:
            type: array
            description: Allowed HTTP methods
            items:
              type: string
              enum: [GET, POST, PUT, DELETE, PATCH]
            default: [POST]
      responseConfig:
        type: object
        description: Response configuration for the webhook
        properties:
          status:
            type: number
            description: HTTP status code to return
            default: 200
            minimum: 100
            maximum: 599
          headers:
            type: array
            description: Response headers as table entries
            items:
              type: object
              properties:
                id:
                  type: string
                  description: Unique identifier for the header entry
                key:
                  type: string
                  description: Header name
                value:
                  type: string
                  description: Header value
                cells:
                  type: object
                  description: Cell display values for the table interface
                  properties:
                    Key:
                      type: string
                      description: Display value for the key column
                    Value:
                      type: string
                      description: Display value for the value column
          body:
            type: string
            description: Response body content
  connections:
    type: object
    properties:
      success:
        type: string
        description: Target block ID for successful webhook processing
      error:
        type: string
        description: Target block ID for error handling
```

## Connection Configuration

Connections define where the workflow goes based on webhook processing:

```yaml
connections:
  success: <string>                     # Target block ID for successful processing
  error: <string>                       # Target block ID for error handling (optional)
```

## Examples

### Basic Webhook Trigger

```yaml
github-webhook:
  type: webhook
  name: "GitHub Webhook"
  inputs:
    webhookConfig:
      enabled: true
      secret: "{{GITHUB_WEBHOOK_SECRET}}"
      methods: [POST]
      headers:
        - key: "X-GitHub-Event"
          value: "push"
    responseConfig:
      status: 200
      body: |
        {
          "message": "Webhook received successfully",
          "timestamp": "{{new Date().toISOString()}}"
        }
  connections:
    success: process-github-event
    error: webhook-error-handler
```

### Slack Event Webhook

```yaml
slack-events:
  type: webhook
  name: "Slack Events"
  inputs:
    webhookConfig:
      enabled: true
      secret: "{{SLACK_SIGNING_SECRET}}"
      methods: [POST]
      headers:
        - key: "Content-Type"
          value: "application/json"
    responseConfig:
      status: 200
      headers:
        - key: "Content-Type"
          value: "application/json"
      body: |
        {
          "challenge": "<webhook.challenge>"
        }
  connections:
    success: handle-slack-event
```

### Payment Webhook (Stripe)

```yaml
stripe-webhook:
  type: webhook
  name: "Stripe Payment Webhook"
  inputs:
    webhookConfig:
      enabled: true
      secret: "{{STRIPE_WEBHOOK_SECRET}}"
      methods: [POST]
      headers:
        - key: "Stripe-Signature"
          value: "*"
    responseConfig:
      status: 200
      headers:
        - key: "Content-Type"
          value: "application/json"
      body: |
        {
          "received": true
        }
  connections:
    success: process-payment-event
    error: payment-webhook-error
```

### Webhook with Complete Table Header Format

When headers are created through the UI table interface, the YAML includes additional metadata:

```yaml
api-webhook-complete:
  type: webhook
  name: "API Webhook with Table Headers"
  inputs:
    webhookConfig:
      enabled: true
      methods: [POST]
      headers:
        - id: header-1-uuid-here
          key: "Authorization"
          value: "Bearer {{WEBHOOK_API_KEY}}"
          cells:
            Key: "Authorization"
            Value: "Bearer {{WEBHOOK_API_KEY}}"
        - id: header-2-uuid-here
          key: "Content-Type"
          value: "application/json"
          cells:
            Key: "Content-Type"
            Value: "application/json"
    responseConfig:
      status: 200
      headers:
        - id: response-header-1-uuid
          key: "Content-Type"
          value: "application/json"
          cells:
            Key: "Content-Type"
            Value: "application/json"
        - id: response-header-2-uuid
          key: "X-Webhook-Response"
          value: "processed"
          cells:
            Key: "X-Webhook-Response"
            Value: "processed"
      body: |
        {
          "status": "received",
          "timestamp": "{{new Date().toISOString()}}"
        }
  connections:
    success: process-webhook-complete
```

### Generic API Webhook

```yaml
api-webhook:
  type: webhook
  name: "API Webhook"
  inputs:
    webhookConfig:
      enabled: true
      methods: [POST, PUT]
      headers:
        - key: "Authorization"
          value: "Bearer {{WEBHOOK_API_KEY}}"
        - key: "Content-Type"
          value: "application/json"
    responseConfig:
      status: 202
      headers:
        - key: "Content-Type"
          value: "application/json"
        - key: "X-Processed-By"
          value: "Sim"
      body: |
        {
          "status": "accepted",
          "id": "{{Math.random().toString(36).substr(2, 9)}}",
          "received_at": "{{new Date().toISOString()}}"
        }
  connections:
    success: process-webhook-data
```

### Multi-Method Webhook

```yaml
crud-webhook:
  type: webhook
  name: "CRUD Webhook"
  inputs:
    webhookConfig:
      enabled: true
      methods: [GET, POST, PUT, DELETE]
      headers:
        - key: "X-API-Key"
          value: "{{CRUD_API_KEY}}"
    responseConfig:
      status: 200
      headers:
        - key: "Content-Type"
          value: "application/json"
      body: |
        {
          "method": "<webhook.method>",
          "processed": true,
          "timestamp": "{{new Date().toISOString()}}"
        }
  connections:
    success: route-by-method
```

## Table Parameter Formats

The Webhook block supports two formats for headers (both validation headers and response headers):

### Simplified Format (Manual YAML)

When writing YAML manually, you can use the simplified format:

```yaml
headers:
  - key: "Authorization"
    value: "Bearer {{API_TOKEN}}"
  - key: "Content-Type"
    value: "application/json"
```

### Complete Table Format (UI Generated)

When headers are created through the UI table interface, the YAML includes additional metadata:

```yaml
headers:
  - id: unique-identifier-here
    key: "Authorization"
    value: "Bearer {{API_TOKEN}}"
    cells:
      Key: "Authorization"
      Value: "Bearer {{API_TOKEN}}"
```

**Key Differences:**
- `id`: Unique identifier for tracking the table row
- `cells`: Display values used by the UI table interface
- Both formats are functionally equivalent for webhook processing
- The complete format preserves UI state when importing/exporting workflows

**Important:** Always quote header names and values that contain special characters:

```yaml
headers:
  - id: auth-header-uuid
    cells:
      Key: "Authorization"
      Value: "Bearer {{WEBHOOK_API_KEY}}"
  - id: content-type-uuid
    cells:
      Key: "Content-Type"
      Value: "application/json"
```

## Webhook Variables

Inside webhook-triggered workflows, these special variables are available:

```yaml
# Available in blocks after the webhook
<webhook.payload>               # Full request payload/body
<webhook.headers>               # Request headers
<webhook.method>                # HTTP method used
<webhook.query>                 # Query parameters
<webhook.path>                  # Request path
<webhook.challenge>             # Challenge parameter (for verification)
```

## Output References

After a webhook processes a request, you can reference its data:

```yaml
# In subsequent blocks
process-webhook:
  inputs:
    payload: <webhook-name.payload>      # Request payload
    headers: <webhook-name.headers>      # Request headers
    method: <webhook-name.method>        # HTTP method
```

## Security Best Practices

- Always use webhook secrets for verification
- Validate expected headers and methods
- Implement proper error handling
- Use HTTPS endpoints in production
- Monitor webhook activity and failures
- Set appropriate response timeouts
- Validate payload structure before processing 