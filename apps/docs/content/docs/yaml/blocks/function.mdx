---
title: Function Block YAML Schema
description: YAML configuration reference for Function blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
  - inputs
properties:
  type:
    type: string
    enum: [function]
    description: Block type identifier
  name:
    type: string
    description: Display name for this function block
  inputs:
    type: object
    required:
      - code
    properties:
      code:
        type: string
        description: JavaScript/TypeScript code to execute (multiline string)
      timeout:
        type: number
        description: Maximum execution time in milliseconds
        default: 30000
        minimum: 1000
        maximum: 300000
  connections:
    type: object
    properties:
      success:
        type: string
        description: Target block ID for successful execution
      error:
        type: string
        description: Target block ID for error handling
```

## Connection Configuration

Connections define where the workflow goes based on execution results:

```yaml
connections:
  success: <string>                     # Target block ID for successful execution
  error: <string>                       # Target block ID for error handling (optional)
```

## Examples

### Simple Validation

```yaml
input-validator:
  type: function
  name: "Input Validator"
  inputs:
    code: |-
      // Check if input number is greater than 5
      const inputValue = parseInt(<start.input>, 10);
      
      if (inputValue > 5) {
        return { 
          valid: true, 
          value: inputValue,
          message: "Input is valid"
        };
      } else {
        return { 
          valid: false, 
          value: inputValue,
          message: "Input must be greater than 5"
        };
      }
  connections:
    success: next-step
    error: handle-error
```

### Data Processing

```yaml
data-processor:
  type: function
  name: "Data Transformer"
  inputs:
    code: |
      // Transform the input data
      const rawData = <start.input>;
      
      // Process and clean the data
      const processed = rawData
        .filter(item => item.status === 'active')
        .map(item => ({
          id: item.id,
          name: item.name.trim(),
          date: new Date(item.created).toISOString()
        }));
      
      return processed;
  connections:
    success: api-save
    error: error-handler
```

### API Integration

```yaml
api-formatter:
  type: function
  name: "Format API Request"
  inputs:
    code: |
      // Prepare data for API submission
      const userData = <agent.response>;
      
      const apiPayload = {
        timestamp: new Date().toISOString(),
        data: userData,
        source: "workflow-automation",
        version: "1.0"
      };
      
      return apiPayload;
  connections:
    success: api-call
```

### Calculations

```yaml
calculator:
  type: function
  name: "Calculate Results"
  inputs:
    code: |
      // Perform calculations on input data
      const numbers = <start.input>;
      
      const sum = numbers.reduce((a, b) => a + b, 0);
      const average = sum / numbers.length;
      const max = Math.max(...numbers);
      const min = Math.min(...numbers);
      
      return {
        sum,
        average,
        max,
        min,
        count: numbers.length
      };
  connections:
    success: results-display
``` 