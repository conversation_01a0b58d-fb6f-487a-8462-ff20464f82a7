---
title: Agent Block YAML Schema
description: YAML configuration reference for Agent blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
properties:
  type:
    type: string
    enum: [agent]
    description: Block type identifier
  name:
    type: string
    description: Display name for this agent block
  inputs:
    type: object
    properties:
      systemPrompt:
        type: string
        description: Instructions that define the agent's role and behavior
      userPrompt:
        type: string
        description: Input content to process (can reference other blocks)
      model:
        type: string
        description: AI model identifier (e.g., gpt-4o, gemini-2.5-pro, deepseek-chat)
      temperature:
        type: number
        minimum: 0
        maximum: 2
        description: Response creativity level (varies by model)
      apiKey:
        type: string
        description: API key for the model provider (use {{ENV_VAR}} format)
      azureEndpoint:
        type: string
        description: Azure OpenAI endpoint URL (required for Azure models)
      azureApiVersion:
        type: string
        description: Azure API version (required for Azure models)
      memories:
        type: string
        description: Memory context from memory blocks
      tools:
        type: array
        description: List of external tools the agent can use
        items:
          type: object
          required: [type, title, toolId, operation, usageControl]
          properties:
            type:
              type: string
              description: Tool type identifier
            title:
              type: string
              description: Human-readable display name
            toolId:
              type: string
              description: Internal tool identifier
            operation:
              type: string
              description: Tool operation/method name
            usageControl:
              type: string
              enum: [auto, required, none]
              description: When AI can use the tool
            params:
              type: object
              description: Tool-specific configuration parameters
            isExpanded:
              type: boolean
              description: UI state
              default: false
      responseFormat:
        type: object
        description: JSON Schema to enforce structured output
    required:
      - model
      - apiKey
  connections:
    type: object
    properties:
      success:
        type: string
        description: Target block ID for successful execution
      error:
        type: string
        description: Target block ID for error handling
```

## Tool Configuration

Tools are defined as an array where each tool has this structure:

```yaml
tools:
  - type: <string>                      # Tool type identifier (exa, gmail, slack, etc.)
    title: <string>                     # Human-readable display name
    toolId: <string>                    # Internal tool identifier
    operation: <string>                 # Tool operation/method name
    usageControl: <string>              # When AI can use it (auto | required | none)
    params: <object>                    # Tool-specific configuration parameters
    isExpanded: <boolean>               # UI state (optional, default: false)
```

## Connection Configuration

Connections define where the workflow goes based on execution results:

```yaml
connections:
  success: <string>                     # Target block ID for successful execution
  error: <string>                       # Target block ID for error handling (optional)
```

## Examples

### Basic Agent

```yaml
content-agent:
  type: agent
  name: "Content Analyzer 1"
  inputs:
    systemPrompt: "You are a helpful content analyzer. Be concise and clear."
    userPrompt: <start.input>
    model: gpt-4o
    temperature: 0.3
    apiKey: '{{OPENAI_API_KEY}}'
  connections:
    success: summary-block

summary-block:
  type: agent
  name: "Summary Generator"
  inputs:
    systemPrompt: "Create a brief summary of the analysis."
    userPrompt: "Analyze this: <contentanalyzer1.content>"
    model: gpt-4o
    apiKey: '{{OPENAI_API_KEY}}'
  connections:
    success: final-step
```

### Agent with Tools

```yaml
research-agent:
  type: agent
  name: "Research Assistant"
  inputs:
    systemPrompt: "Research the topic and provide detailed information."
    userPrompt: <start.input>
    model: gpt-4o
    apiKey: '{{OPENAI_API_KEY}}'
    tools:
      - type: exa
        title: "Web Search"
        toolId: exa_search
        operation: exa_search
        usageControl: auto
        params:
          apiKey: '{{EXA_API_KEY}}'
  connections:
    success: summary-block
```

### Structured Output

```yaml
data-extractor:
  type: agent
  name: "Extract Contact Info"
  inputs:
    systemPrompt: "Extract contact information from the text."
    userPrompt: <start.input>
    model: gpt-4o
    apiKey: '{{OPENAI_API_KEY}}'
    responseFormat: |
      {
        "name": "contact_extraction",
        "schema": {
          "type": "object",
          "properties": {
            "name": {"type": "string"},
            "email": {"type": "string"},
            "phone": {"type": "string"}
          },
          "required": ["name"]
        },
        "strict": true
      }
  connections:
    success: save-contact
```

### Azure OpenAI

```yaml
azure-agent:
  type: agent
  name: "Azure AI Assistant"
  inputs:
    systemPrompt: "You are a helpful assistant."
    userPrompt: <start.input>
    model: gpt-4o
    apiKey: '{{AZURE_OPENAI_API_KEY}}'
    azureEndpoint: '{{AZURE_OPENAI_ENDPOINT}}'
    azureApiVersion: "2024-07-01-preview"
  connections:
    success: response-block
``` 