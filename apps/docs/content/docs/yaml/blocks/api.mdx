---
title: API Block YAML Schema
description: YAML configuration reference for API blocks
---

## Schema Definition

```yaml
type: object
required:
  - type
  - name
  - inputs
properties:
  type:
    type: string
    enum: [api]
    description: Block type identifier
  name:
    type: string
    description: Display name for this API block
  inputs:
    type: object
    required:
      - url
      - method
    properties:
      url:
        type: string
        description: The endpoint URL to send the request to
      method:
        type: string
        enum: [GET, POST, PUT, DELETE, PATCH]
        description: HTTP method for the request
        default: GET
      params:
        type: array
        description: Query parameters as table entries
        items:
          type: object
          required:
            - id
            - cells
          properties:
            id:
              type: string
              description: Unique identifier for the parameter entry
            cells:
              type: object
              required:
                - Key
                - Value
              properties:
                Key:
                  type: string
                  description: Parameter name
                Value:
                  type: string
                  description: Parameter value
      headers:
        type: array
        description: HTTP headers as table entries
        items:
          type: object
          required:
            - id
            - cells
          properties:
            id:
              type: string
              description: Unique identifier for the header entry
            cells:
              type: object
              required:
                - Key
                - Value
              properties:
                Key:
                  type: string
                  description: Header name
                Value:
                  type: string
                  description: Header value
      body:
        type: string
        description: Request body for POST/PUT/PATCH methods
      timeout:
        type: number
        description: Request timeout in milliseconds
        default: 30000
        minimum: 1000
        maximum: 300000
  connections:
    type: object
    properties:
      success:
        type: string
        description: Target block ID for successful requests
      error:
        type: string
        description: Target block ID for error handling
```

## Connection Configuration

Connections define where the workflow goes based on request results:

```yaml
connections:
  success: <string>                     # Target block ID for successful requests
  error: <string>                       # Target block ID for error handling (optional)
```

## Examples

### Simple GET Request

```yaml
user-api:
  type: api
  name: "Fetch User Data"
  inputs:
    url: "https://api.example.com/users/123"
    method: GET
    headers:
      - id: header-1-uuid-here
        cells:
          Key: "Authorization"
          Value: "Bearer {{API_TOKEN}}"
      - id: header-2-uuid-here
        cells:
          Key: "Content-Type"
          Value: "application/json"
  connections:
    success: process-user-data
    error: handle-api-error
```



### POST Request with Body

```yaml
create-ticket:
  type: api
  name: "Create Support Ticket"
  inputs:
    url: "https://api.support.com/tickets"
    method: POST
    headers:
      - id: auth-header-uuid
        cells:
          Key: "Authorization"
          Value: "Bearer {{SUPPORT_API_KEY}}"
      - id: content-type-uuid
        cells:
          Key: "Content-Type"
          Value: "application/json"
    body: |
      {
        "title": "<agent.title>",
        "description": "<agent.description>",
        "priority": "high"
      }
  connections:
    success: ticket-created
    error: ticket-error
```

### Dynamic URL with Query Parameters

```yaml
search-api:
  type: api
  name: "Search Products"
  inputs:
    url: "https://api.store.com/products"
    method: GET
    params:
      - id: search-param-uuid
        cells:
          Key: "q"
          Value: <start.searchTerm>
      - id: limit-param-uuid
        cells:
          Key: "limit"
          Value: "10"
      - id: category-param-uuid
        cells:
          Key: "category"
          Value: <filter.category>
    headers:
      - id: auth-header-uuid
        cells:
          Key: "Authorization"
          Value: "Bearer {{STORE_API_KEY}}"
  connections:
    success: display-results
```

## Parameter Format

Headers and params (query parameters) use the table format with the following structure:

```yaml
headers:
  - id: unique-identifier-here
    cells:
      Key: "Content-Type"
      Value: "application/json"
  - id: another-unique-identifier
    cells:
      Key: "Authorization"
      Value: "Bearer {{API_TOKEN}}"

params:
  - id: param-identifier-here
    cells:
      Key: "limit"
      Value: "10"
```

**Structure Details:**
- `id`: Unique identifier for tracking the table row
- `cells.Key`: The parameter/header name
- `cells.Value`: The parameter/header value
- This format allows for proper table management and UI state preservation

## Output References

After an API block executes, you can reference its outputs in subsequent blocks. The API block provides three main outputs:

### Available Outputs

| Output | Type | Description |
|--------|------|-------------|
| `data` | any | The response body/payload from the API |
| `status` | number | HTTP status code (200, 404, 500, etc.) |
| `headers` | object | Response headers returned by the server |

### Usage Examples

```yaml
# Reference API response data
process-data:
  type: function
  name: "Process API Data"
  inputs:
    code: |
      const responseData = <fetchuserdata.data>;
      const statusCode = <fetchuserdata.status>;
      const responseHeaders = <fetchuserdata.headers>;
      
      if (statusCode === 200) {
        return {
          success: true,
          user: responseData,
          contentType: responseHeaders['content-type']
        };
      } else {
        return {
          success: false,
          error: `API call failed with status ${statusCode}`
        };
      }

# Use API data in an agent block
analyze-response:
  type: agent
  name: "Analyze Response"
  inputs:
    userPrompt: |
      Analyze this API response:
      
      Status: <fetchuserdata.status>
      Data: <fetchuserdata.data>
      
      Provide insights about the response.

# Conditional logic based on status
check-status:
  type: condition
  name: "Check API Status"
  inputs:
    condition: <fetchuserdata.status> === 200
  connections:
    true: success-handler
    false: error-handler
```

### Practical Example

```yaml
user-api:
  type: api
  name: "Fetch User Data"
  inputs:
    url: "https://api.example.com/users/123"
    method: GET
  connections:
    success: process-response

process-response:
  type: function
  name: "Process Response"
  inputs:
    code: |
      const user = <fetchuserdata.data>;
      const status = <fetchuserdata.status>;
      
      console.log(`API returned status: ${status}`);
      console.log(`User data:`, user);
      
      return {
        userId: user.id,
        email: user.email,
        isActive: status === 200
      };
```

### Error Handling

```yaml
api-with-error-handling:
  type: api
  name: "API Call"
  inputs:
    url: "https://api.example.com/data"
    method: GET
  connections:
    success: check-response
    error: handle-error

check-response:
  type: condition
  name: "Check Response Status"
  inputs:
    condition: <apicall.status> >= 200 && <apicall.status> < 300
  connections:
    true: process-success
    false: handle-api-error

process-success:
  type: function
  name: "Process Success"
  inputs:
    code: |
      return {
        success: true,
        data: <apicall.data>,
        message: "API call successful"
      };

handle-api-error:
  type: function
  name: "Handle API Error"
  inputs:
    code: |
      return {
        success: false,
        status: <apicall.status>,
        error: "API call failed",
        data: <apicall.data>
      };
```

## YAML String Escaping

When writing YAML, certain strings must be quoted to be properly parsed:

### Strings That Must Be Quoted

```yaml
# URLs with hyphens, colons, special characters
url: "https://api.example.com/users/123"
url: "https://my-api.example.com/data"

# Header values with hyphens or special characters  
headers:
  - id: header-uuid
    cells:
      Key: "User-Agent"
      Value: "My-Application/1.0"
  - id: auth-uuid
    cells:
      Key: "Authorization" 
      Value: "Bearer my-token-123"

# Parameter values with hyphens
params:
  - id: param-uuid
    cells:
      Key: "sort-by"
      Value: "created-at"
```

### When to Use Quotes

- ✅ **Always quote**: URLs, tokens, values with hyphens, colons, or special characters
- ✅ **Always quote**: Values that start with numbers but should be strings
- ✅ **Always quote**: Boolean-looking strings that should remain as strings
- ❌ **Don't quote**: Simple alphanumeric strings without special characters

### Examples

```yaml
# ✅ Correct
url: "https://api.stripe.com/v1/charges"
headers:
  - id: auth-header
    cells:
      Key: "Authorization"
      Value: "Bearer sk-test-123456789"

# ❌ Incorrect (may cause parsing errors)
url: https://api.stripe.com/v1/charges
headers:
  - id: auth-header
    cells:
      Key: Authorization
      Value: Bearer sk-test-123456789
```

## Best Practices

- Use environment variables for API keys: `{{API_KEY_NAME}}`
- Include error handling with error connections
- Set appropriate timeouts for your use case
- Validate response status codes in subsequent blocks
- Use meaningful block names for easier reference
- **Always quote strings with special characters, URLs, and tokens** 