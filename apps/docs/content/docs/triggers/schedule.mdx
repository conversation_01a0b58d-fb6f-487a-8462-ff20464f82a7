---
title: Schedule
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Image } from '@/components/ui/image'
import { Video } from '@/components/ui/video'

The Schedule block automatically triggers workflows on a recurring schedule at specified intervals or times.

<div className="flex justify-center">
  <Image
    src="/static/schedule.png"
    alt="Schedule Block"
    width={500}
    height={400}
    className="my-6"
  />
</div>

## Schedule Options

Configure when your workflow runs using the dropdown options:

<Tabs items={['Simple Intervals', 'Cron Expressions']}>
  <Tab>
    <ul className="list-disc space-y-1 pl-6">
      <li><strong>Every few minutes</strong>: 5, 15, 30 minute intervals</li>
      <li><strong>Hourly</strong>: Every hour or every few hours</li>
      <li><strong>Daily</strong>: Once or multiple times per day</li>
      <li><strong>Weekly</strong>: Specific days of the week</li>
      <li><strong>Monthly</strong>: Specific days of the month</li>
    </ul>
  </Tab>
  <Tab>
    <p>Use cron expressions for advanced scheduling:</p>
    <div className="text-sm space-y-1">
      <div><code>0 9 * * 1-5</code> - Every weekday at 9 AM</div>
      <div><code>*/15 * * * *</code> - Every 15 minutes</div>
      <div><code>0 0 1 * *</code> - First day of each month</div>
    </div>
  </Tab>
</Tabs>

## Configuring Schedules

<div className="mx-auto w-full overflow-hidden rounded-lg">
  <Video src="configure-schedule.mp4" width={700} height={450} />
</div>

When a workflow is scheduled:
- The schedule becomes **active** and shows the next execution time
- Click the **"Scheduled"** button to deactivate the schedule
- Schedules automatically deactivate after **3 consecutive failures**

## Disabled Schedules

<div className="flex justify-center">
  <Image
    src="/static/schedule-disabled.png"
    alt="Disabled Schedule"
    width={500}
    height={350}
    className="my-6"
  />
</div>

Disabled schedules show when they were last active and can be re-enabled at any time.

<Callout>
Schedule blocks cannot receive incoming connections and serve as pure workflow triggers.
</Callout>