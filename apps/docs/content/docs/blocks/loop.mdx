---
title: Loop
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Step, Steps } from 'fumadocs-ui/components/steps'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'
import { Image } from '@/components/ui/image'

The Loop block is a container block in Sim that allows you to create iterative workflows by executing a group of blocks repeatedly. Loops enable iterative processing in your workflows.

The Loop block supports two types of iteration:

<Callout type="info">
  Loop blocks are container nodes that can hold other blocks inside them. The blocks inside a loop will execute multiple times based on your configuration.
</Callout>

## Overview

The Loop block enables you to:

<Steps>
  <Step>
    <strong>Iterate over collections</strong>: Process arrays or objects one item at a time
  </Step>
  <Step>
    <strong>Repeat operations</strong>: Execute blocks a fixed number of times
  </Step>
  <Step>
    <strong>Sequential processing</strong>: Handle data transformation in ordered iterations
  </Step>
  <Step>
    <strong>Aggregate results</strong>: Collect outputs from all loop iterations
  </Step>
</Steps>

## How It Works

The Loop block executes contained blocks through sequential iteration:

1. **Initialize Loop** - Set up iteration parameters (count or collection)
2. **Execute Iteration** - Run contained blocks for current iteration
3. **Collect Results** - Store output from each iteration
4. **Continue or Complete** - Move to next iteration or finish loop

## Configuration Options

### Loop Type

Choose between two types of loops:

<Tabs items={['For Loop', 'ForEach Loop']}>
  <Tab>
    **For Loop (Iterations)** - A numeric loop that executes a fixed number of times:
    
    <div className="flex justify-center">
      <Image
        src="/static/blocks/loop-1.png"
        alt="For Loop with iterations"
        width={500}
        height={400}
        className="my-6"
      />
    </div>
    
    Use this when you need to repeat an operation a specific number of times.
    
    ```
    Example: Run 5 times
    - Iteration 1
    - Iteration 2
    - Iteration 3
    - Iteration 4
    - Iteration 5
    ```
  </Tab>
  <Tab>
    **ForEach Loop (Collection)** - A collection-based loop that iterates over each item in an array or object:
    
    <div className="flex justify-center">
      <Image
        src="/static/blocks/loop-2.png"
        alt="ForEach Loop with collection"
        width={500}
        height={400}
        className="my-6"
      />
    </div>
    
    Use this when you need to process a collection of items.
    
    ```
    Example: Process ["apple", "banana", "orange"]
    - Iteration 1: Process "apple"
    - Iteration 2: Process "banana"
    - Iteration 3: Process "orange"
    ```
  </Tab>
</Tabs>

## How to Use Loops

### Creating a Loop

1. Drag a Loop block from the toolbar onto your canvas
2. Configure the loop type and parameters
3. Drag other blocks inside the loop container
4. Connect the blocks as needed

### Accessing Results

After a loop completes, you can access aggregated results:

- **`<loop.results>`**: Array of results from all loop iterations

## Example Use Cases

### Processing API Results

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Process multiple customer records</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>API block fetches customer list</li>
    <li>ForEach loop iterates over each customer</li>
    <li>Inside loop: Agent analyzes customer data</li>
    <li>Inside loop: Function stores analysis results</li>
  </ol>
</div>

### Iterative Content Generation

<div className="mb-4 rounded-md border p-4">
  <h4 className="font-medium">Scenario: Generate multiple variations</h4>
  <ol className="list-decimal pl-5 text-sm">
    <li>Set For loop to 5 iterations</li>
    <li>Inside loop: Agent generates content variation</li>
    <li>Inside loop: Evaluator scores the content</li>
    <li>After loop: Function selects best variation</li>
  </ol>
</div>

## Advanced Features

### Limitations

<Callout type="warning">
  Container blocks (Loops and Parallels) cannot be nested inside each other. This means:
  - You cannot place a Loop block inside another Loop block
  - You cannot place a Parallel block inside a Loop block
  - You cannot place any container block inside another container block
  
  If you need multi-dimensional iteration, consider restructuring your workflow to use sequential loops or process data in stages.
</Callout>

<Callout type="info">
  Loops execute sequentially, not in parallel. If you need concurrent execution, use the Parallel block instead.
</Callout>

## Inputs and Outputs

<Tabs items={['Configuration', 'Variables', 'Results']}>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>Loop Type</strong>: Choose between 'for' or 'forEach'
      </li>
      <li>
        <strong>Iterations</strong>: Number of times to execute (for loops)
      </li>
      <li>
        <strong>Collection</strong>: Array or object to iterate over (forEach loops)
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>loop.currentItem</strong>: Current item being processed
      </li>
      <li>
        <strong>loop.index</strong>: Current iteration number (0-based)
      </li>
      <li>
        <strong>loop.items</strong>: Full collection (forEach loops)
      </li>
    </ul>
  </Tab>
  <Tab>
    <ul className="list-disc space-y-2 pl-6">
      <li>
        <strong>loop.results</strong>: Array of all iteration results
      </li>
      <li>
        <strong>Structure</strong>: Results maintain iteration order
      </li>
      <li>
        <strong>Access</strong>: Available in blocks after the loop
      </li>
    </ul>
  </Tab>
</Tabs>

## Best Practices

- **Set reasonable limits**: Keep iteration counts reasonable to avoid long execution times
- **Use ForEach for collections**: When processing arrays or objects, use ForEach instead of For loops
- **Handle errors gracefully**: Consider adding error handling inside loops for robust workflows
