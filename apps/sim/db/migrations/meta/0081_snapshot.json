{"id": "a556d60d-15b1-4d71-9b93-43ddfd59fe68", "prevId": "65bb6869-89f6-4392-9280-6e56b59871cd", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_key": {"name": "api_key", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"api_key_user_id_user_id_fk": {"name": "api_key_user_id_user_id_fk", "tableFrom": "api_key", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_key_key_unique": {"name": "api_key_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "subdomain": {"name": "subdomain", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "customizations": {"name": "customizations", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}, "auth_type": {"name": "auth_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'public'"}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "allowed_emails": {"name": "allowed_emails", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'"}, "output_configs": {"name": "output_configs", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subdomain_idx": {"name": "subdomain_idx", "columns": [{"expression": "subdomain", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_workflow_id_workflow_id_fk": {"name": "chat_workflow_id_workflow_id_fk", "tableFrom": "chat", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_user_id_user_id_fk": {"name": "chat_user_id_user_id_fk", "tableFrom": "chat", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.copilot_chats": {"name": "copilot_chats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "messages": {"name": "messages", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'"}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true, "default": "'claude-3-7-sonnet-latest'"}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": false}, "preview_yaml": {"name": "preview_yaml", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"copilot_chats_user_id_idx": {"name": "copilot_chats_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_chats_workflow_id_idx": {"name": "copilot_chats_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_chats_user_workflow_idx": {"name": "copilot_chats_user_workflow_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_chats_created_at_idx": {"name": "copilot_chats_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_chats_updated_at_idx": {"name": "copilot_chats_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"copilot_chats_user_id_user_id_fk": {"name": "copilot_chats_user_id_user_id_fk", "tableFrom": "copilot_chats", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "copilot_chats_workflow_id_workflow_id_fk": {"name": "copilot_chats_workflow_id_workflow_id_fk", "tableFrom": "copilot_chats", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.copilot_feedback": {"name": "copilot_feedback", "schema": "", "columns": {"feedback_id": {"name": "feedback_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_query": {"name": "user_query", "type": "text", "primaryKey": false, "notNull": true}, "agent_response": {"name": "agent_response", "type": "text", "primaryKey": false, "notNull": true}, "is_positive": {"name": "is_positive", "type": "boolean", "primaryKey": false, "notNull": true}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "workflow_yaml": {"name": "workflow_yaml", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"copilot_feedback_user_id_idx": {"name": "copilot_feedback_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_feedback_chat_id_idx": {"name": "copilot_feedback_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_feedback_user_chat_idx": {"name": "copilot_feedback_user_chat_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_feedback_is_positive_idx": {"name": "copilot_feedback_is_positive_idx", "columns": [{"expression": "is_positive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "copilot_feedback_created_at_idx": {"name": "copilot_feedback_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"copilot_feedback_user_id_user_id_fk": {"name": "copilot_feedback_user_id_user_id_fk", "tableFrom": "copilot_feedback", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "copilot_feedback_chat_id_copilot_chats_id_fk": {"name": "copilot_feedback_chat_id_copilot_chats_id_fk", "tableFrom": "copilot_feedback", "tableTo": "copilot_chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.custom_tools": {"name": "custom_tools", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "schema": {"name": "schema", "type": "json", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"custom_tools_user_id_user_id_fk": {"name": "custom_tools_user_id_user_id_fk", "tableFrom": "custom_tools", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.docs_embeddings": {"name": "docs_embeddings", "schema": "", "columns": {"chunk_id": {"name": "chunk_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chunk_text": {"name": "chunk_text", "type": "text", "primaryKey": false, "notNull": true}, "source_document": {"name": "source_document", "type": "text", "primaryKey": false, "notNull": true}, "source_link": {"name": "source_link", "type": "text", "primaryKey": false, "notNull": true}, "header_text": {"name": "header_text", "type": "text", "primaryKey": false, "notNull": true}, "header_level": {"name": "header_level", "type": "integer", "primaryKey": false, "notNull": true}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}, "embedding_model": {"name": "embedding_model", "type": "text", "primaryKey": false, "notNull": true, "default": "'text-embedding-3-small'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'"}, "chunk_text_tsv": {"name": "chunk_text_tsv", "type": "tsvector", "primaryKey": false, "notNull": false, "generated": {"as": "to_tsvector('english', \"docs_embeddings\".\"chunk_text\")", "type": "stored"}}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"docs_emb_source_document_idx": {"name": "docs_emb_source_document_idx", "columns": [{"expression": "source_document", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "docs_emb_header_level_idx": {"name": "docs_emb_header_level_idx", "columns": [{"expression": "header_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "docs_emb_source_header_idx": {"name": "docs_emb_source_header_idx", "columns": [{"expression": "source_document", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "header_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "docs_emb_model_idx": {"name": "docs_emb_model_idx", "columns": [{"expression": "embedding_model", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "docs_emb_created_at_idx": {"name": "docs_emb_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "docs_embedding_vector_hnsw_idx": {"name": "docs_embedding_vector_hnsw_idx", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {"m": 16, "ef_construction": 64}}, "docs_emb_metadata_gin_idx": {"name": "docs_emb_metadata_gin_idx", "columns": [{"expression": "metadata", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}, "docs_emb_chunk_text_fts_idx": {"name": "docs_emb_chunk_text_fts_idx", "columns": [{"expression": "chunk_text_tsv", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"docs_embedding_not_null_check": {"name": "docs_embedding_not_null_check", "value": "\"embedding\" IS NOT NULL"}, "docs_header_level_check": {"name": "docs_header_level_check", "value": "\"header_level\" >= 1 AND \"header_level\" <= 6"}}, "isRLSEnabled": false}, "public.document": {"name": "document", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "knowledge_base_id": {"name": "knowledge_base_id", "type": "text", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "chunk_count": {"name": "chunk_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "character_count": {"name": "character_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "processing_status": {"name": "processing_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "processing_started_at": {"name": "processing_started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "processing_completed_at": {"name": "processing_completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "processing_error": {"name": "processing_error", "type": "text", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "tag1": {"name": "tag1", "type": "text", "primaryKey": false, "notNull": false}, "tag2": {"name": "tag2", "type": "text", "primaryKey": false, "notNull": false}, "tag3": {"name": "tag3", "type": "text", "primaryKey": false, "notNull": false}, "tag4": {"name": "tag4", "type": "text", "primaryKey": false, "notNull": false}, "tag5": {"name": "tag5", "type": "text", "primaryKey": false, "notNull": false}, "tag6": {"name": "tag6", "type": "text", "primaryKey": false, "notNull": false}, "tag7": {"name": "tag7", "type": "text", "primaryKey": false, "notNull": false}, "uploaded_at": {"name": "uploaded_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"doc_kb_id_idx": {"name": "doc_kb_id_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_filename_idx": {"name": "doc_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_kb_uploaded_at_idx": {"name": "doc_kb_uploaded_at_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "uploaded_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_processing_status_idx": {"name": "doc_processing_status_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "processing_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag1_idx": {"name": "doc_tag1_idx", "columns": [{"expression": "tag1", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag2_idx": {"name": "doc_tag2_idx", "columns": [{"expression": "tag2", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag3_idx": {"name": "doc_tag3_idx", "columns": [{"expression": "tag3", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag4_idx": {"name": "doc_tag4_idx", "columns": [{"expression": "tag4", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag5_idx": {"name": "doc_tag5_idx", "columns": [{"expression": "tag5", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag6_idx": {"name": "doc_tag6_idx", "columns": [{"expression": "tag6", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_tag7_idx": {"name": "doc_tag7_idx", "columns": [{"expression": "tag7", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_knowledge_base_id_knowledge_base_id_fk": {"name": "document_knowledge_base_id_knowledge_base_id_fk", "tableFrom": "document", "tableTo": "knowledge_base", "columnsFrom": ["knowledge_base_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.embedding": {"name": "embedding", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "knowledge_base_id": {"name": "knowledge_base_id", "type": "text", "primaryKey": false, "notNull": true}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "chunk_index": {"name": "chunk_index", "type": "integer", "primaryKey": false, "notNull": true}, "chunk_hash": {"name": "chunk_hash", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "content_length": {"name": "content_length", "type": "integer", "primaryKey": false, "notNull": true}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": false}, "embedding_model": {"name": "embedding_model", "type": "text", "primaryKey": false, "notNull": true, "default": "'text-embedding-3-small'"}, "start_offset": {"name": "start_offset", "type": "integer", "primaryKey": false, "notNull": true}, "end_offset": {"name": "end_offset", "type": "integer", "primaryKey": false, "notNull": true}, "tag1": {"name": "tag1", "type": "text", "primaryKey": false, "notNull": false}, "tag2": {"name": "tag2", "type": "text", "primaryKey": false, "notNull": false}, "tag3": {"name": "tag3", "type": "text", "primaryKey": false, "notNull": false}, "tag4": {"name": "tag4", "type": "text", "primaryKey": false, "notNull": false}, "tag5": {"name": "tag5", "type": "text", "primaryKey": false, "notNull": false}, "tag6": {"name": "tag6", "type": "text", "primaryKey": false, "notNull": false}, "tag7": {"name": "tag7", "type": "text", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "content_tsv": {"name": "content_tsv", "type": "tsvector", "primaryKey": false, "notNull": false, "generated": {"as": "to_tsvector('english', \"embedding\".\"content\")", "type": "stored"}}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"emb_kb_id_idx": {"name": "emb_kb_id_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_doc_id_idx": {"name": "emb_doc_id_idx", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_doc_chunk_idx": {"name": "emb_doc_chunk_idx", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chunk_index", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "emb_kb_model_idx": {"name": "emb_kb_model_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "embedding_model", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_kb_enabled_idx": {"name": "emb_kb_enabled_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "enabled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_doc_enabled_idx": {"name": "emb_doc_enabled_idx", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "enabled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "embedding_vector_hnsw_idx": {"name": "embedding_vector_hnsw_idx", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {"m": 16, "ef_construction": 64}}, "emb_tag1_idx": {"name": "emb_tag1_idx", "columns": [{"expression": "tag1", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_tag2_idx": {"name": "emb_tag2_idx", "columns": [{"expression": "tag2", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_tag3_idx": {"name": "emb_tag3_idx", "columns": [{"expression": "tag3", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_tag4_idx": {"name": "emb_tag4_idx", "columns": [{"expression": "tag4", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_tag5_idx": {"name": "emb_tag5_idx", "columns": [{"expression": "tag5", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_tag6_idx": {"name": "emb_tag6_idx", "columns": [{"expression": "tag6", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_tag7_idx": {"name": "emb_tag7_idx", "columns": [{"expression": "tag7", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "emb_content_fts_idx": {"name": "emb_content_fts_idx", "columns": [{"expression": "content_tsv", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}}, "foreignKeys": {"embedding_knowledge_base_id_knowledge_base_id_fk": {"name": "embedding_knowledge_base_id_knowledge_base_id_fk", "tableFrom": "embedding", "tableTo": "knowledge_base", "columnsFrom": ["knowledge_base_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "embedding_document_id_document_id_fk": {"name": "embedding_document_id_document_id_fk", "tableFrom": "embedding", "tableTo": "document", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"embedding_not_null_check": {"name": "embedding_not_null_check", "value": "\"embedding\" IS NOT NULL"}}, "isRLSEnabled": false}, "public.environment": {"name": "environment", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "variables": {"name": "variables", "type": "json", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"environment_user_id_user_id_fk": {"name": "environment_user_id_user_id_fk", "tableFrom": "environment", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"environment_user_id_unique": {"name": "environment_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitation": {"name": "invitation", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invitation_inviter_id_user_id_fk": {"name": "invitation_inviter_id_user_id_fk", "tableFrom": "invitation", "tableTo": "user", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitation_organization_id_organization_id_fk": {"name": "invitation_organization_id_organization_id_fk", "tableFrom": "invitation", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.knowledge_base": {"name": "knowledge_base", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "embedding_model": {"name": "embedding_model", "type": "text", "primaryKey": false, "notNull": true, "default": "'text-embedding-3-small'"}, "embedding_dimension": {"name": "embedding_dimension", "type": "integer", "primaryKey": false, "notNull": true, "default": 1536}, "chunking_config": {"name": "chunking_config", "type": "json", "primaryKey": false, "notNull": true, "default": "'{\"maxSize\": 1024, \"minSize\": 1, \"overlap\": 200}'"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"kb_user_id_idx": {"name": "kb_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "kb_workspace_id_idx": {"name": "kb_workspace_id_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "kb_user_workspace_idx": {"name": "kb_user_workspace_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "kb_deleted_at_idx": {"name": "kb_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"knowledge_base_user_id_user_id_fk": {"name": "knowledge_base_user_id_user_id_fk", "tableFrom": "knowledge_base", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "knowledge_base_workspace_id_workspace_id_fk": {"name": "knowledge_base_workspace_id_workspace_id_fk", "tableFrom": "knowledge_base", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.knowledge_base_tag_definitions": {"name": "knowledge_base_tag_definitions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "knowledge_base_id": {"name": "knowledge_base_id", "type": "text", "primaryKey": false, "notNull": true}, "tag_slot": {"name": "tag_slot", "type": "text", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": true}, "field_type": {"name": "field_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'text'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"kb_tag_definitions_kb_slot_idx": {"name": "kb_tag_definitions_kb_slot_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tag_slot", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "kb_tag_definitions_kb_display_name_idx": {"name": "kb_tag_definitions_kb_display_name_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "display_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "kb_tag_definitions_kb_id_idx": {"name": "kb_tag_definitions_kb_id_idx", "columns": [{"expression": "knowledge_base_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"knowledge_base_tag_definitions_knowledge_base_id_knowledge_base_id_fk": {"name": "knowledge_base_tag_definitions_knowledge_base_id_knowledge_base_id_fk", "tableFrom": "knowledge_base_tag_definitions", "tableTo": "knowledge_base", "columnsFrom": ["knowledge_base_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.marketplace": {"name": "marketplace", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "json", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "text", "primaryKey": false, "notNull": true}, "author_name": {"name": "author_name", "type": "text", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"marketplace_workflow_id_workflow_id_fk": {"name": "marketplace_workflow_id_workflow_id_fk", "tableFrom": "marketplace", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "marketplace_author_id_user_id_fk": {"name": "marketplace_author_id_user_id_fk", "tableFrom": "marketplace", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.member": {"name": "member", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"member_user_id_user_id_fk": {"name": "member_user_id_user_id_fk", "tableFrom": "member", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "member_organization_id_organization_id_fk": {"name": "member_organization_id_organization_id_fk", "tableFrom": "member", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.memory": {"name": "memory", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"memory_key_idx": {"name": "memory_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "memory_workflow_idx": {"name": "memory_workflow_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "memory_workflow_key_idx": {"name": "memory_workflow_key_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"memory_workflow_id_workflow_id_fk": {"name": "memory_workflow_id_workflow_id_fk", "tableFrom": "memory", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "org_usage_limit": {"name": "org_usage_limit", "type": "numeric", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "text", "primaryKey": false, "notNull": true}, "permission_type": {"name": "permission_type", "type": "permission_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"permissions_user_id_idx": {"name": "permissions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "permissions_entity_idx": {"name": "permissions_entity_idx", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "permissions_user_entity_type_idx": {"name": "permissions_user_entity_type_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "permissions_user_entity_permission_idx": {"name": "permissions_user_entity_permission_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "permission_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "permissions_user_entity_idx": {"name": "permissions_user_entity_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "permissions_unique_constraint": {"name": "permissions_unique_constraint", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"permissions_user_id_user_id_fk": {"name": "permissions_user_id_user_id_fk", "tableFrom": "permissions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "active_organization_id": {"name": "active_organization_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "session_active_organization_id_organization_id_fk": {"name": "session_active_organization_id_organization_id_fk", "tableFrom": "session", "tableTo": "organization", "columnsFrom": ["active_organization_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.settings": {"name": "settings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": true, "default": "'system'"}, "auto_connect": {"name": "auto_connect", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "auto_fill_env_vars": {"name": "auto_fill_env_vars", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "auto_pan": {"name": "auto_pan", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "console_expanded_by_default": {"name": "console_expanded_by_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "telemetry_enabled": {"name": "telemetry_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "email_preferences": {"name": "email_preferences", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'"}, "general": {"name": "general", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"settings_user_id_user_id_fk": {"name": "settings_user_id_user_id_fk", "tableFrom": "settings", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"settings_user_id_unique": {"name": "settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription": {"name": "subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "period_start": {"name": "period_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "period_end": {"name": "period_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false}, "trial_start": {"name": "trial_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_end": {"name": "trial_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"subscription_reference_status_idx": {"name": "subscription_reference_status_idx", "columns": [{"expression": "reference_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"check_enterprise_metadata": {"name": "check_enterprise_metadata", "value": "plan != 'enterprise' OR metadata IS NOT NULL"}}, "isRLSEnabled": false}, "public.template_stars": {"name": "template_stars", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": true}, "starred_at": {"name": "starred_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"template_stars_user_id_idx": {"name": "template_stars_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "template_stars_template_id_idx": {"name": "template_stars_template_id_idx", "columns": [{"expression": "template_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "template_stars_user_template_idx": {"name": "template_stars_user_template_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "template_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "template_stars_template_user_idx": {"name": "template_stars_template_user_idx", "columns": [{"expression": "template_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "template_stars_starred_at_idx": {"name": "template_stars_starred_at_idx", "columns": [{"expression": "starred_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "template_stars_template_starred_at_idx": {"name": "template_stars_template_starred_at_idx", "columns": [{"expression": "template_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "starred_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "template_stars_user_template_unique": {"name": "template_stars_user_template_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "template_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"template_stars_user_id_user_id_fk": {"name": "template_stars_user_id_user_id_fk", "tableFrom": "template_stars", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "template_stars_template_id_templates_id_fk": {"name": "template_stars_template_id_templates_id_fk", "tableFrom": "template_stars", "tableTo": "templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.templates": {"name": "templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "author": {"name": "author", "type": "text", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "stars": {"name": "stars", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3972F6'"}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": true, "default": "'FileText'"}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"templates_workflow_id_idx": {"name": "templates_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_user_id_idx": {"name": "templates_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_category_idx": {"name": "templates_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_views_idx": {"name": "templates_views_idx", "columns": [{"expression": "views", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_stars_idx": {"name": "templates_stars_idx", "columns": [{"expression": "stars", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_category_views_idx": {"name": "templates_category_views_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "views", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_category_stars_idx": {"name": "templates_category_stars_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stars", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_user_category_idx": {"name": "templates_user_category_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_created_at_idx": {"name": "templates_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "templates_updated_at_idx": {"name": "templates_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"templates_workflow_id_workflow_id_fk": {"name": "templates_workflow_id_workflow_id_fk", "tableFrom": "templates", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "templates_user_id_user_id_fk": {"name": "templates_user_id_user_id_fk", "tableFrom": "templates", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_rate_limits": {"name": "user_rate_limits", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "sync_api_requests": {"name": "sync_api_requests", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "async_api_requests": {"name": "async_api_requests", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "window_start": {"name": "window_start", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_request_at": {"name": "last_request_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_rate_limited": {"name": "is_rate_limited", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "rate_limit_reset_at": {"name": "rate_limit_reset_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_rate_limits_user_id_user_id_fk": {"name": "user_rate_limits_user_id_user_id_fk", "tableFrom": "user_rate_limits", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_stats": {"name": "user_stats", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "total_manual_executions": {"name": "total_manual_executions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_api_calls": {"name": "total_api_calls", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_webhook_triggers": {"name": "total_webhook_triggers", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_scheduled_executions": {"name": "total_scheduled_executions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_chat_executions": {"name": "total_chat_executions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_tokens_used": {"name": "total_tokens_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_cost": {"name": "total_cost", "type": "numeric", "primaryKey": false, "notNull": true, "default": "'0'"}, "current_usage_limit": {"name": "current_usage_limit", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'10'"}, "usage_limit_updated_at": {"name": "usage_limit_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "current_period_cost": {"name": "current_period_cost", "type": "numeric", "primaryKey": false, "notNull": true, "default": "'0'"}, "last_period_cost": {"name": "last_period_cost", "type": "numeric", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_copilot_cost": {"name": "total_copilot_cost", "type": "numeric", "primaryKey": false, "notNull": true, "default": "'0'"}, "total_copilot_tokens": {"name": "total_copilot_tokens", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_copilot_calls": {"name": "total_copilot_calls", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "billing_blocked": {"name": "billing_blocked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"user_stats_user_id_user_id_fk": {"name": "user_stats_user_id_user_id_fk", "tableFrom": "user_stats", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_stats_user_id_unique": {"name": "user_stats_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.waitlist": {"name": "waitlist", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"waitlist_email_unique": {"name": "waitlist_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook": {"name": "webhook", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "block_id": {"name": "block_id", "type": "text", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": false}, "provider_config": {"name": "provider_config", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"path_idx": {"name": "path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"webhook_workflow_id_workflow_id_fk": {"name": "webhook_workflow_id_workflow_id_fk", "tableFrom": "webhook", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "webhook_block_id_workflow_blocks_id_fk": {"name": "webhook_block_id_workflow_blocks_id_fk", "tableFrom": "webhook", "tableTo": "workflow_blocks", "columnsFrom": ["block_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow": {"name": "workflow", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "text", "primaryKey": false, "notNull": false}, "folder_id": {"name": "folder_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3972F6'"}, "last_synced": {"name": "last_synced", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_deployed": {"name": "is_deployed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "deployed_state": {"name": "deployed_state", "type": "json", "primaryKey": false, "notNull": false}, "deployed_at": {"name": "deployed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "pinned_api_key": {"name": "pinned_api_key", "type": "text", "primaryKey": false, "notNull": false}, "collaborators": {"name": "collaborators", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'"}, "run_count": {"name": "run_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_run_at": {"name": "last_run_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "variables": {"name": "variables", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "marketplace_data": {"name": "marketplace_data", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"workflow_user_id_idx": {"name": "workflow_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_workspace_id_idx": {"name": "workflow_workspace_id_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_user_workspace_idx": {"name": "workflow_user_workspace_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_user_id_user_id_fk": {"name": "workflow_user_id_user_id_fk", "tableFrom": "workflow", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_workspace_id_workspace_id_fk": {"name": "workflow_workspace_id_workspace_id_fk", "tableFrom": "workflow", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_folder_id_workflow_folder_id_fk": {"name": "workflow_folder_id_workflow_folder_id_fk", "tableFrom": "workflow", "tableTo": "workflow_folder", "columnsFrom": ["folder_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_blocks": {"name": "workflow_blocks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "position_x": {"name": "position_x", "type": "numeric", "primaryKey": false, "notNull": true}, "position_y": {"name": "position_y", "type": "numeric", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "horizontal_handles": {"name": "horizontal_handles", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_wide": {"name": "is_wide", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "advanced_mode": {"name": "advanced_mode", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "trigger_mode": {"name": "trigger_mode", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": true, "default": "'0'"}, "sub_blocks": {"name": "sub_blocks", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'"}, "outputs": {"name": "outputs", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'"}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'"}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "extent": {"name": "extent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_blocks_workflow_id_idx": {"name": "workflow_blocks_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_blocks_parent_id_idx": {"name": "workflow_blocks_parent_id_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_blocks_workflow_parent_idx": {"name": "workflow_blocks_workflow_parent_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_blocks_workflow_type_idx": {"name": "workflow_blocks_workflow_type_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_blocks_workflow_id_workflow_id_fk": {"name": "workflow_blocks_workflow_id_workflow_id_fk", "tableFrom": "workflow_blocks", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_checkpoints": {"name": "workflow_checkpoints", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": false}, "workflow_state": {"name": "workflow_state", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_checkpoints_user_id_idx": {"name": "workflow_checkpoints_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_workflow_id_idx": {"name": "workflow_checkpoints_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_chat_id_idx": {"name": "workflow_checkpoints_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_message_id_idx": {"name": "workflow_checkpoints_message_id_idx", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_user_workflow_idx": {"name": "workflow_checkpoints_user_workflow_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_workflow_chat_idx": {"name": "workflow_checkpoints_workflow_chat_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_created_at_idx": {"name": "workflow_checkpoints_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_checkpoints_chat_created_at_idx": {"name": "workflow_checkpoints_chat_created_at_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_checkpoints_user_id_user_id_fk": {"name": "workflow_checkpoints_user_id_user_id_fk", "tableFrom": "workflow_checkpoints", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_checkpoints_workflow_id_workflow_id_fk": {"name": "workflow_checkpoints_workflow_id_workflow_id_fk", "tableFrom": "workflow_checkpoints", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_checkpoints_chat_id_copilot_chats_id_fk": {"name": "workflow_checkpoints_chat_id_copilot_chats_id_fk", "tableFrom": "workflow_checkpoints", "tableTo": "copilot_chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_edges": {"name": "workflow_edges", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "source_block_id": {"name": "source_block_id", "type": "text", "primaryKey": false, "notNull": true}, "target_block_id": {"name": "target_block_id", "type": "text", "primaryKey": false, "notNull": true}, "source_handle": {"name": "source_handle", "type": "text", "primaryKey": false, "notNull": false}, "target_handle": {"name": "target_handle", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_edges_workflow_id_idx": {"name": "workflow_edges_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_edges_source_block_idx": {"name": "workflow_edges_source_block_idx", "columns": [{"expression": "source_block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_edges_target_block_idx": {"name": "workflow_edges_target_block_idx", "columns": [{"expression": "target_block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_edges_workflow_source_idx": {"name": "workflow_edges_workflow_source_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "source_block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_edges_workflow_target_idx": {"name": "workflow_edges_workflow_target_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "target_block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_edges_workflow_id_workflow_id_fk": {"name": "workflow_edges_workflow_id_workflow_id_fk", "tableFrom": "workflow_edges", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_edges_source_block_id_workflow_blocks_id_fk": {"name": "workflow_edges_source_block_id_workflow_blocks_id_fk", "tableFrom": "workflow_edges", "tableTo": "workflow_blocks", "columnsFrom": ["source_block_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_edges_target_block_id_workflow_blocks_id_fk": {"name": "workflow_edges_target_block_id_workflow_blocks_id_fk", "tableFrom": "workflow_edges", "tableTo": "workflow_blocks", "columnsFrom": ["target_block_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_execution_logs": {"name": "workflow_execution_logs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "execution_id": {"name": "execution_id", "type": "text", "primaryKey": false, "notNull": true}, "state_snapshot_id": {"name": "state_snapshot_id", "type": "text", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "text", "primaryKey": false, "notNull": true}, "trigger": {"name": "trigger", "type": "text", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "total_duration_ms": {"name": "total_duration_ms", "type": "integer", "primaryKey": false, "notNull": false}, "execution_data": {"name": "execution_data", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'"}, "cost": {"name": "cost", "type": "jsonb", "primaryKey": false, "notNull": false}, "files": {"name": "files", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_execution_logs_workflow_id_idx": {"name": "workflow_execution_logs_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_execution_logs_execution_id_idx": {"name": "workflow_execution_logs_execution_id_idx", "columns": [{"expression": "execution_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_execution_logs_trigger_idx": {"name": "workflow_execution_logs_trigger_idx", "columns": [{"expression": "trigger", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_execution_logs_level_idx": {"name": "workflow_execution_logs_level_idx", "columns": [{"expression": "level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_execution_logs_started_at_idx": {"name": "workflow_execution_logs_started_at_idx", "columns": [{"expression": "started_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_execution_logs_execution_id_unique": {"name": "workflow_execution_logs_execution_id_unique", "columns": [{"expression": "execution_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "workflow_execution_logs_workflow_started_at_idx": {"name": "workflow_execution_logs_workflow_started_at_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "started_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_execution_logs_workflow_id_workflow_id_fk": {"name": "workflow_execution_logs_workflow_id_workflow_id_fk", "tableFrom": "workflow_execution_logs", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_execution_logs_state_snapshot_id_workflow_execution_snapshots_id_fk": {"name": "workflow_execution_logs_state_snapshot_id_workflow_execution_snapshots_id_fk", "tableFrom": "workflow_execution_logs", "tableTo": "workflow_execution_snapshots", "columnsFrom": ["state_snapshot_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_execution_snapshots": {"name": "workflow_execution_snapshots", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "state_hash": {"name": "state_hash", "type": "text", "primaryKey": false, "notNull": true}, "state_data": {"name": "state_data", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_snapshots_workflow_id_idx": {"name": "workflow_snapshots_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_snapshots_hash_idx": {"name": "workflow_snapshots_hash_idx", "columns": [{"expression": "state_hash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_snapshots_workflow_hash_idx": {"name": "workflow_snapshots_workflow_hash_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "state_hash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "workflow_snapshots_created_at_idx": {"name": "workflow_snapshots_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_execution_snapshots_workflow_id_workflow_id_fk": {"name": "workflow_execution_snapshots_workflow_id_workflow_id_fk", "tableFrom": "workflow_execution_snapshots", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_folder": {"name": "workflow_folder", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#6B7280'"}, "is_expanded": {"name": "is_expanded", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_folder_user_idx": {"name": "workflow_folder_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_folder_workspace_parent_idx": {"name": "workflow_folder_workspace_parent_idx", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_folder_parent_sort_idx": {"name": "workflow_folder_parent_sort_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "sort_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_folder_user_id_user_id_fk": {"name": "workflow_folder_user_id_user_id_fk", "tableFrom": "workflow_folder", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_folder_workspace_id_workspace_id_fk": {"name": "workflow_folder_workspace_id_workspace_id_fk", "tableFrom": "workflow_folder", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_schedule": {"name": "workflow_schedule", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "block_id": {"name": "block_id", "type": "text", "primaryKey": false, "notNull": false}, "cron_expression": {"name": "cron_expression", "type": "text", "primaryKey": false, "notNull": false}, "next_run_at": {"name": "next_run_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_ran_at": {"name": "last_ran_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "trigger_type": {"name": "trigger_type", "type": "text", "primaryKey": false, "notNull": true}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": true, "default": "'UTC'"}, "failed_count": {"name": "failed_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_failed_at": {"name": "last_failed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_schedule_workflow_block_unique": {"name": "workflow_schedule_workflow_block_unique", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "block_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_schedule_workflow_id_workflow_id_fk": {"name": "workflow_schedule_workflow_id_workflow_id_fk", "tableFrom": "workflow_schedule", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_schedule_block_id_workflow_blocks_id_fk": {"name": "workflow_schedule_block_id_workflow_blocks_id_fk", "tableFrom": "workflow_schedule", "tableTo": "workflow_blocks", "columnsFrom": ["block_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_subflows": {"name": "workflow_subflows", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workflow_subflows_workflow_id_idx": {"name": "workflow_subflows_workflow_id_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "workflow_subflows_workflow_type_idx": {"name": "workflow_subflows_workflow_type_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_subflows_workflow_id_workflow_id_fk": {"name": "workflow_subflows_workflow_id_workflow_id_fk", "tableFrom": "workflow_subflows", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspace": {"name": "workspace", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workspace_owner_id_user_id_fk": {"name": "workspace_owner_id_user_id_fk", "tableFrom": "workspace", "tableTo": "user", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspace_environment": {"name": "workspace_environment", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "text", "primaryKey": false, "notNull": true}, "variables": {"name": "variables", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"workspace_environment_workspace_unique": {"name": "workspace_environment_workspace_unique", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workspace_environment_workspace_id_workspace_id_fk": {"name": "workspace_environment_workspace_id_workspace_id_fk", "tableFrom": "workspace_environment", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspace_invitation": {"name": "workspace_invitation", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "permission_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'admin'"}, "org_invitation_id": {"name": "org_invitation_id", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workspace_invitation_workspace_id_workspace_id_fk": {"name": "workspace_invitation_workspace_id_workspace_id_fk", "tableFrom": "workspace_invitation", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workspace_invitation_inviter_id_user_id_fk": {"name": "workspace_invitation_inviter_id_user_id_fk", "tableFrom": "workspace_invitation", "tableTo": "user", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspace_invitation_token_unique": {"name": "workspace_invitation_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.permission_type": {"name": "permission_type", "schema": "public", "values": ["admin", "write", "read"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}