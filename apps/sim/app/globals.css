@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   WORKFLOW COMPONENT Z-INDEX FIXES
   ========================================================================== */
.workflow-container .react-flow__edges {
  z-index: 0 !important;
}

.workflow-container .react-flow__node {
  z-index: 21 !important;
}

.workflow-container .react-flow__node-loopNode,
.workflow-container .react-flow__node-parallelNode,
.workflow-container .react-flow__node-subflowNode {
  z-index: -1 !important;
}

.workflow-container .react-flow__handle {
  z-index: 30 !important;
}

.workflow-container .react-flow__edge [data-testid="workflow-edge"] {
  z-index: 0 !important;
}

/* ==========================================================================
   THEME SYSTEM - CSS CUSTOM PROPERTIES
   ========================================================================== */
@layer base {
  /* Light Mode Theme */
  :root,
  .light {
    /* Core Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    /* Card Colors */
    --card: 0 0% 99.2%;
    --card-foreground: 0 0% 3.9%;

    /* Popover Colors */
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* Primary Colors */
    --primary: 0 0% 11.2%;
    --primary-foreground: 0 0% 98%;

    /* Secondary Colors */
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 11.2%;

    /* Muted Colors */
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 46.9%;

    /* Accent Colors */
    --accent: 0 0% 92.5%;
    --accent-foreground: 0 0% 11.2%;

    /* Destructive Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Border & Input Colors */
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    /* Border Radius */
    --radius: 0.5rem;

    /* Scrollbar Properties */
    --scrollbar-track: 0 0% 85%;
    --scrollbar-thumb: 0 0% 65%;
    --scrollbar-thumb-hover: 0 0% 55%;
    --scrollbar-size: 8px;

    /* Workflow Properties */
    --workflow-background: 0 0% 100%;
    --workflow-dots: 0 0% 94.5%;
    --card-background: 0 0% 99.2%;
    --card-border: 0 0% 89.8%;
    --card-text: 0 0% 3.9%;
    --card-hover: 0 0% 96.1%;

    /* Base Component Properties */
    --base-muted-foreground: #737373;

    /* Gradient Colors */
    --gradient-primary: 263 85% 70%; /* More vibrant purple */
    --gradient-secondary: 336 95% 65%; /* More vibrant pink */

    /* Brand Colors (Default Sim Theme) */
    --brand-primary-hex: #701ffc; /* Primary brand purple */
    --brand-primary-hover-hex: #802fff; /* Primary brand purple hover */
    --brand-secondary-hex: #6518e6; /* Secondary brand purple */
    --brand-accent-hex: #9d54ff; /* Accent purple for links */
    --brand-accent-hover-hex: #a66fff; /* Accent purple hover */
    --brand-background-hex: #0c0c0c; /* Primary dark background */

    /* UI Surface Colors */
    --surface-elevated: #202020; /* Elevated surface background for dark mode */
  }

  /* Dark Mode Theme */
  .dark {
    /* Core Colors */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    /* Card Colors */
    --card: 0 0% 9.0%;
    --card-foreground: 0 0% 98%;

    /* Popover Colors */
    --popover: 0 0% 9.0%;
    --popover-foreground: 0 0% 98%;

    /* Primary Colors */
    --primary: 0 0% 11.2%;
    --primary-foreground: 0 0% 98%;

    /* Secondary Colors */
    --secondary: 0 0% 12.0%;
    --secondary-foreground: 0 0% 98%;

    /* Muted Colors */
    --muted: 0 0% 17.5%;
    --muted-foreground: 0 0% 65.1%;

    /* Accent Colors */
    --accent: 0 0% 17.5%;
    --accent-foreground: 0 0% 98%;

    /* Destructive Colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* Border & Input Colors */
    --border: 0 0% 16.1%;
    --input: 0 0% 16.1%;
    --ring: 0 0% 83.9%;

    /* Scrollbar Properties */
    --scrollbar-track: 0 0% 17.5%;
    --scrollbar-thumb: 0 0% 30%;
    --scrollbar-thumb-hover: 0 0% 40%;

    /* Workflow Properties */
    --workflow-background: 0 0% 3.9%;
    --workflow-dots: 0 0% 7.5%;
    --card-background: 0 0% 9.0%;
    --card-border: 0 0% 22.7%;
    --card-text: 0 0% 98%;
    --card-hover: 0 0% 12.0%;

    /* Base Component Properties */
    --base-muted-foreground: #a3a3a3;

    /* Gradient Colors - Adjusted for dark mode */
    --gradient-primary: 263 90% 75%; /* More vibrant purple for dark mode */
    --gradient-secondary: 336 100% 72%; /* More vibrant pink for dark mode */

    /* Brand Colors (Same in dark mode) */
    --brand-primary-hex: #701ffc; /* Primary brand purple */
    --brand-primary-hover-hex: #802fff; /* Primary brand purple hover */
    --brand-secondary-hex: #6518e6; /* Secondary brand purple */
    --brand-accent-hex: #9d54ff; /* Accent purple for links */
    --brand-accent-hover-hex: #a66fff; /* Accent purple hover */
    --brand-background-hex: #0c0c0c; /* Primary dark background */

    /* UI Surface Colors */
    --surface-elevated: #202020; /* Elevated surface background for dark mode */
  }
}

/* ==========================================================================
   BASE STYLES
   ========================================================================== */
@layer base {
  * {
    @apply border-border;
    overscroll-behavior-x: none;
  }

  body {
    @apply bg-background text-foreground;
    overscroll-behavior-x: none;
    overscroll-behavior-y: none;
    min-height: 100vh;
    /* Prevent layout shifts when scrollbar appears/disappears */
    scrollbar-gutter: stable;
    /* Improve animation performance */
    text-rendering: optimizeSpeed;
  }

  /* Global Scrollbar Styling */
  ::-webkit-scrollbar {
    width: var(--scrollbar-size);
    height: var(--scrollbar-size);
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: var(--radius);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.3);
  }

  /* For Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }
}

/* ==========================================================================
   TYPOGRAPHY
   ========================================================================== */
.font-geist-sans {
  font-family: var(--font-geist-sans);
}

.font-geist-mono {
  font-family: var(--font-geist-mono);
}

/* ==========================================================================
   PANEL STYLES
   ========================================================================== */
.panel-tab-base {
  color: var(--base-muted-foreground);
}

.panel-tab-active {
  /* Light Mode Panel Tab Active */
  background-color: #f5f5f5;
  color: #1a1a1a;
  border-color: #e5e5e5;
}

/* Dark Mode Panel Tab Active */
.dark .panel-tab-active {
  background-color: #1f1f1f;
  color: #ffffff;
  border-color: #424242;
}

.panel-tab-inactive:hover {
  background-color: hsl(var(--secondary));
  color: hsl(var(--card-foreground));
}

/* ==========================================================================
   DARK MODE OVERRIDES
   ========================================================================== */
.dark .error-badge {
  background-color: hsl(0, 70%, 20%) !important;
  /* Darker red background for dark mode */
  color: hsl(0, 0%, 100%) !important;
  /* Pure white text for better contrast */
}

.dark .bg-red-500 {
  @apply bg-red-700;
}

/* ==========================================================================
   BROWSER INPUT OVERRIDES
   ========================================================================== */
/* Chrome, Safari, Edge, Opera */
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
}

/* Firefox */
input[type="search"]::-moz-search-cancel-button {
  display: none;
}

/* Microsoft Edge */
input[type="search"]::-ms-clear {
  display: none;
}

/* ==========================================================================
   LAYOUT UTILITIES
   ========================================================================== */
.main-content-overlay {
  z-index: 40;
  /* Higher z-index to appear above content */
}

/* ==========================================================================
   ANIMATIONS & UTILITIES
   ========================================================================== */
@layer utilities {
  /* Animation Performance */
  .animation-container {
    contain: paint layout style;
    will-change: opacity, transform;
  }

  /* Scrollbar Utilities */
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    /* For Webkit browsers (Chrome, Safari, Edge) */
    -webkit-scrollbar: none;
    -webkit-scrollbar-width: none;
    -webkit-scrollbar-track: transparent;
    -webkit-scrollbar-thumb: transparent;

    /* For Firefox */
    scrollbar-width: none;
    scrollbar-color: transparent transparent;

    /* For Internet Explorer and Edge Legacy */
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }

  .scrollbar-hide::-webkit-scrollbar-track {
    display: none;
    background: transparent;
  }

  .scrollbar-hide::-webkit-scrollbar-thumb {
    display: none;
    background: transparent;
  }

  /* Gradient Text Utility - Use with Tailwind gradient directions */
  .gradient-text {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Brand Color Utilities */
  .bg-brand-primary {
    background-color: var(--brand-primary-hex);
  }

  .bg-brand-primary-hover {
    background-color: var(--brand-primary-hover-hex);
  }

  .bg-brand-secondary {
    background-color: var(--brand-secondary-hex);
  }

  .bg-brand-accent {
    background-color: var(--brand-accent-hex);
  }

  .bg-brand-background {
    background-color: var(--brand-background-hex);
  }

  .text-brand-primary {
    color: var(--brand-primary-hex);
  }

  .text-brand-accent {
    color: var(--brand-accent-hex);
  }

  .text-brand-accent-hover {
    color: var(--brand-accent-hover-hex);
  }

  .border-brand-primary {
    border-color: var(--brand-primary-hex);
  }

  .hover\:bg-brand-primary-hover:hover {
    background-color: var(--brand-primary-hover-hex);
  }

  .hover\:bg-brand-secondary:hover {
    background-color: var(--brand-secondary-hex);
  }

  .hover\:text-brand-accent-hover:hover {
    color: var(--brand-accent-hover-hex);
  }

  /* Surface Utilities */
  .bg-surface-elevated {
    background-color: var(--surface-elevated);
  }

  /* Animation Classes */
  .animate-pulse-ring {
    animation: pulse-ring 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .transition-ring {
    transition-property: box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .animate-orbit {
    animation: orbit calc(var(--duration, 2) * 1s) linear infinite;
  }

  .animate-marquee {
    animation: marquee var(--duration) infinite linear;
  }

  .animate-marquee-vertical {
    animation: marquee-vertical var(--duration) linear infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out forwards;
  }

  /* Special Effects */
  .streaming-effect {
    @apply relative overflow-hidden;
  }

  .streaming-effect::after {
    content: "";
    @apply pointer-events-none absolute left-0 top-0 h-full w-full;
    background: linear-gradient(
      90deg,
      rgba(128, 128, 128, 0) 0%,
      rgba(128, 128, 128, 0.1) 50%,
      rgba(128, 128, 128, 0) 100%
    );
    animation: code-shimmer 1.5s infinite;
    z-index: 10;
  }

  .dark .streaming-effect::after {
    background: linear-gradient(
      90deg,
      rgba(180, 180, 180, 0) 0%,
      rgba(180, 180, 180, 0.1) 50%,
      rgba(180, 180, 180, 0) 100%
    );
  }

  .loading-placeholder::placeholder {
    animation: placeholder-pulse 1.5s ease-in-out infinite;
  }

  /* ==========================================================================
     KEYFRAME ANIMATIONS
     ========================================================================== */
  @keyframes pulse-ring {
    0% {
      box-shadow: 0 0 0 0 hsl(var(--border));
    }

    50% {
      box-shadow: 0 0 0 8px hsl(var(--border));
    }

    100% {
      box-shadow: 0 0 0 0 hsl(var(--border));
    }
  }

  @keyframes code-shimmer {
    0% {
      transform: translateX(-100%);
    }

    100% {
      transform: translateX(100%);
    }
  }

  @keyframes orbit {
    0% {
      transform: rotate(calc(var(--angle) * 1deg)) translateY(calc(var(--radius) * 1px))
        rotate(calc(var(--angle) * -1deg));
    }

    100% {
      transform: rotate(calc(var(--angle) * 1deg + 360deg)) translateY(calc(var(--radius) * 1px))
        rotate(calc((var(--angle) * -1deg) - 360deg));
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }

    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }

    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes placeholder-pulse {
    0%,
    100% {
      opacity: 0.5;
    }

    50% {
      opacity: 0.8;
    }
  }
}
