# Local K3s values for SimStudio
# Optimized for Rancher Desktop local development

# Global configuration
global:
  imageRegistry: "ghcr.io"
  storageClass: "local-path"  # K3s default storage class

# Main application
app:
  enabled: true
  replicaCount: 1
  
  # Resource allocation for local development
  resources:
    limits:
      memory: "3Gi"
      cpu: "1500m"
    requests:
      memory: "1536Mi"
      cpu: "500m"
  
  # Local development URLs - using NodePort services
  env:
    NEXT_PUBLIC_APP_URL: "http://localhost:30000"
    BETTER_AUTH_URL: "http://localhost:30000"
    NEXT_PUBLIC_SOCKET_URL: "http://localhost:30002"
    SOCKET_SERVER_URL: "http://localhost:30002"
    
    # These will be set via --set flags during installation
    BETTER_AUTH_SECRET: ""
    ENCRYPTION_KEY: ""
    
    # Optional: Add your API keys here
    # OPENAI_API_KEY: ""
    # ANTHROPIC_API_KEY_1: ""

    # Ollama URL - set to empty string when ollama is disabled
    OLLAMA_URL: ""
    
  # Use NodePort for local access
  service:
    type: NodePort
    port: 3000
    targetPort: 3000
    nodePort: 30000

# Realtime service
realtime:
  enabled: true
  replicaCount: 1
  
  # Resource allocation for realtime WebSocket service
  resources:
    limits:
      memory: "1Gi"
      cpu: "500m"
    requests:
      memory: "512Mi"
      cpu: "250m"
  
  env:
    NEXT_PUBLIC_APP_URL: "http://localhost:30000"
    BETTER_AUTH_URL: "http://localhost:30000"
    NEXT_PUBLIC_SOCKET_URL: "http://localhost:30002"
    BETTER_AUTH_SECRET: ""  # Will be set via --set flag
    ALLOWED_ORIGINS: "http://localhost:30000"
  
  # Use NodePort for local access
  service:
    type: NodePort
    port: 3002
    targetPort: 3002
    nodePort: 30002

# Database migrations
migrations:
  enabled: true
  
  # Minimal resources for migration job
  resources:
    limits:
      memory: "512Mi"
      cpu: "200m"
    requests:
      memory: "256Mi"
      cpu: "100m"

# PostgreSQL database
postgresql:
  enabled: true
  
  # Authentication for local development
  auth:
    username: postgres
    password: ""  # Will be set via --set flag
    database: simstudio
  
  # PostgreSQL with pgvector extension
  image:
    repository: pgvector/pgvector
    tag: pg17
    pullPolicy: IfNotPresent
  
  # Resource allocation for local PostgreSQL
  resources:
    limits:
      memory: "1Gi"
      cpu: "500m"
    requests:
      memory: "512Mi"
      cpu: "250m"
  
  # Enable persistence with local-path storage
  persistence:
    enabled: true
    storageClass: "local-path"
    size: 5Gi
    accessModes:
      - ReadWriteOnce
  
  # SSL/TLS disabled for local development
  tls:
    enabled: false
  
  # PostgreSQL configuration for local development
  config:
    maxConnections: 200
    sharedBuffers: "256MB"
    maxWalSize: "1GB"
    minWalSize: "80MB"
  
  # Expose PostgreSQL for debugging (optional)
  service:
    type: NodePort
    port: 5432
    targetPort: 5432
    nodePort: 30432

# Ollama for local AI models (optional)
ollama:
  enabled: false  # Set to true if you want local AI models
  
  # Enable if you have GPU support
  gpu:
    enabled: false
  
  # Resource allocation
  resources:
    limits:
      memory: "4Gi"
      cpu: "2000m"
    requests:
      memory: "2Gi"
      cpu: "1000m"
  
  # Persistence for models
  persistence:
    enabled: true
    storageClass: "local-path"
    size: 20Gi
    accessModes:
      - ReadWriteOnce
  
  # Expose Ollama service
  service:
    type: NodePort
    port: 11434
    targetPort: 11434
    nodePort: 30434

# Ingress disabled for local development (using NodePort instead)
ingress:
  enabled: false

# Autoscaling disabled for local development
autoscaling:
  enabled: false

# Pod disruption budget disabled for local development
podDisruptionBudget:
  enabled: false

# Network policies disabled for local development
networkPolicy:
  enabled: false

# Monitoring disabled for local development
monitoring:
  serviceMonitor:
    enabled: false

# Telemetry disabled for local development
telemetry:
  enabled: false

# CronJobs enabled with minimal resources
cronjobs:
  enabled: true
  
  # Minimal resources for cron jobs
  resources:
    limits:
      memory: "64Mi"
      cpu: "50m"
    requests:
      memory: "32Mi"
      cpu: "25m"
